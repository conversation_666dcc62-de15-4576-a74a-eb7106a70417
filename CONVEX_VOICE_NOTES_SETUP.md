# Convex Voice Notes Setup Guide

This guide will help you set up the complete voice notes application with Convex database, external file storage, and AI transcription.

## 🏗️ Architecture Overview

The new Convex-based voice notes system includes:

- **Convex Database**: Real-time database with automatic synchronization
- **External File Storage**: Cloudinary or AWS S3 for audio files
- **AI Transcription**: Deepgram or OpenAI Whisper integration
- **Background Jobs**: Automated transcription processing
- **Real-time Updates**: Live status updates for transcription progress

## 📋 Prerequisites

1. **Convex Account**: Sign up at [convex.dev](https://convex.dev)
2. **Clerk Account**: For authentication (already configured)
3. **External Storage**: Choose one:
   - Cloudinary account (recommended for simplicity)
   - AWS S3 bucket with appropriate permissions
4. **Transcription Service**: Choose one:
   - Deepgram API key (recommended)
   - OpenAI API key for Whisper

## 🚀 Setup Steps

### 1. Install Dependencies

```bash
# Install Convex client
npm install convex

# Install additional dependencies for audio handling
npm install expo-av expo-file-system
```

### 2. Initialize Convex

```bash
# Initialize Convex in your project
npx convex dev

# This will create:
# - convex/ directory with your functions
# - .env.local with CONVEX_DEPLOYMENT
```

### 3. Environment Variables

Add these to your `.env.local` file:

```env
# Convex (automatically added by convex dev)
CONVEX_DEPLOYMENT=your-deployment-url

# External Storage - Choose ONE:

# Option A: Cloudinary (Recommended)
EXPO_PUBLIC_CLOUDINARY_URL=https://api.cloudinary.com/v1_1/your-cloud-name/upload
EXPO_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your-upload-preset

# Option B: AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=your-region
AWS_S3_BUCKET=your-bucket-name

# Transcription Service - Choose ONE:

# Option A: Deepgram (Recommended)
DEEPGRAM_API_KEY=your-deepgram-api-key

# Option B: OpenAI Whisper
OPENAI_API_KEY=your-openai-api-key
```

### 4. Configure External Storage

#### Option A: Cloudinary Setup

1. Create a Cloudinary account at [cloudinary.com](https://cloudinary.com)
2. Go to Settings > Upload presets
3. Create a new unsigned upload preset
4. Set folder to organize uploads (e.g., `voice-notes/`)
5. Enable auto-tagging and other features as needed

#### Option B: AWS S3 Setup

1. Create an S3 bucket in AWS Console
2. Configure CORS policy:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": ["ETag"]
  }
]
```

3. Create IAM user with S3 permissions
4. Generate access keys

### 5. Configure Transcription Service

#### Option A: Deepgram Setup

1. Sign up at [deepgram.com](https://deepgram.com)
2. Get your API key from the dashboard
3. Add to environment variables

#### Option B: OpenAI Setup

1. Get API key from [platform.openai.com](https://platform.openai.com)
2. Add to environment variables

### 6. Deploy Convex Functions

```bash
# Deploy your Convex functions
npx convex deploy

# This will deploy:
# - Database schema
# - Mutations and queries
# - Background job processors
# - Cron jobs for transcription
```

### 7. Update Your App

Replace your existing components with the new Convex-based ones:

```typescript
// In your main app file or navigation
import { ConvexVoiceNotesScreen } from '@/screens/ConvexVoiceNotesScreen';

// Use the new screen in your navigation
<Stack.Screen 
  name="VoiceNotes" 
  component={ConvexVoiceNotesScreen} 
  options={{ title: 'Voice Notes' }}
/>
```

## 🔧 Configuration Options

### Audio Recording Settings

Modify `ConvexVoiceRecorder.tsx` to customize:

```typescript
// High quality recording (default)
Audio.RecordingOptionsPresets.HIGH_QUALITY

// Or use custom settings
{
  android: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
    audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
  },
  ios: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
    audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
    linearPCMBitDepth: 16,
    linearPCMIsBigEndian: false,
    linearPCMIsFloat: false,
  },
}
```

### Transcription Settings

Modify `convex/audioFiles.ts` to customize transcription:

```typescript
// Deepgram settings
{
  model: 'nova-2',        // Latest model
  smart_format: true,     // Auto punctuation
  punctuate: true,        // Add punctuation
  diarize: false,         // Speaker separation
  language: 'en-US',      // Language code
}

// OpenAI Whisper settings
{
  model: 'whisper-1',
  language: 'en',
  response_format: 'text',
  temperature: 0,
}
```

## 📱 Usage Examples

### Basic Voice Recording

```typescript
import { ConvexVoiceRecorder } from '@/components/voice/ConvexVoiceRecorder';

<ConvexVoiceRecorder
  onRecordingComplete={(audioFileId, noteId) => {
    console.log('Recording saved:', { audioFileId, noteId });
  }}
  onError={(error) => {
    console.error('Recording error:', error);
  }}
/>
```

### Custom Note Creation

```typescript
import { useConvexNotesService } from '@/services/convexNotesService';

const { createNote } = useConvexNotesService();

const createVoiceNote = async (audioFileId: Id<'audioFiles'>) => {
  const noteId = await createNote({
    title: 'My Voice Note',
    tags: ['meeting', 'important'],
    blocks: [{
      type: 'AUDIO',
      sortOrder: 0,
      content: {
        audioFileId,
        title: 'Recording',
      }
    }]
  });
};
```

### Real-time Transcription Status

```typescript
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';

const TranscriptionStatus = ({ audioFileId }) => {
  const audioFile = useQuery(api.audioFiles.getAudioFile, { audioFileId });
  
  return (
    <View>
      <Text>Status: {audioFile?.transcriptionStatus}</Text>
      {audioFile?.transcription && (
        <Text>Transcription: {audioFile.transcription}</Text>
      )}
    </View>
  );
};
```

## 🔍 Troubleshooting

### Common Issues

1. **Recording Permission Denied**
   - Ensure audio permissions are requested
   - Check device settings for microphone access

2. **Upload Failures**
   - Verify external storage credentials
   - Check network connectivity
   - Ensure file size limits are not exceeded

3. **Transcription Not Working**
   - Verify API keys are correct
   - Check audio file format compatibility
   - Monitor background job processing

4. **Real-time Updates Not Working**
   - Ensure Convex client is properly configured
   - Check authentication status
   - Verify database permissions

### Debug Mode

Enable debug logging:

```typescript
// Add to your app's entry point
if (__DEV__) {
  console.log('[DEBUG] Convex Voice Notes Debug Mode Enabled');
}
```

### Performance Optimization

1. **Limit Query Results**: Use pagination for large note lists
2. **Optimize Audio Quality**: Balance quality vs file size
3. **Background Processing**: Ensure transcription jobs don't block UI
4. **Caching**: Leverage Convex's built-in caching

## 📊 Monitoring

### Background Jobs

Monitor transcription job processing:

```typescript
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';

const JobStats = () => {
  const stats = useQuery(api.transcriptionJobs.getJobStats, {});
  
  return (
    <View>
      <Text>Success Rate: {stats?.successRate.toFixed(1)}%</Text>
      <Text>Pending: {stats?.queued}</Text>
      <Text>Processing: {stats?.processing}</Text>
    </View>
  );
};
```

### Storage Usage

Track audio file storage:

```typescript
const AudioStats = () => {
  const audioFiles = useQuery(api.audioFiles.getUserAudioFiles, {});
  
  const totalSize = audioFiles?.reduce((sum, file) => sum + file.fileSize, 0) || 0;
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  
  return <Text>Total Storage: {totalSizeMB} MB</Text>;
};
```

## 🚀 Next Steps

1. **Test the Complete Flow**: Record → Upload → Transcribe → View
2. **Customize UI**: Modify components to match your app's design
3. **Add Features**: Implement search, tags, notebooks as needed
4. **Monitor Performance**: Watch for any bottlenecks or issues
5. **Scale**: Consider rate limits and usage quotas for external services

## 📞 Support

If you encounter issues:

1. Check the Convex dashboard for function logs
2. Monitor external service usage and limits
3. Review network requests in development tools
4. Test with different audio formats and lengths

The new Convex-based system provides a robust, scalable foundation for voice notes with real-time synchronization and professional-grade transcription capabilities.
