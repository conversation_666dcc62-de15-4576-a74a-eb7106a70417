-- =====================================================
-- UNIVERSAL NOTES APP - INITIAL SCHEMA MIGRATION
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- 1. PROFILES TABLE (Clerk Integration)
-- =====================================================
CREATE TABLE profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_user_id TEXT UNIQUE NOT NULL, -- Clerk's user ID
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    username TEXT UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL
);

-- Add indexes for performance
CREATE INDEX idx_profiles_clerk_user_id ON profiles(clerk_user_id);
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_created_at ON profiles(created_at);

-- =====================================================
-- 2. NOTEBOOKS TABLE (Note Organization)
-- =====================================================
CREATE TABLE notebooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 100),
    description TEXT CHECK (length(description) <= 500),
    color TEXT DEFAULT '#3B82F6' CHECK (color ~ '^#[0-9A-Fa-f]{6}$'),
    icon TEXT DEFAULT 'book',
    is_default BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL
);

-- Add indexes
CREATE INDEX idx_notebooks_user_id ON notebooks(user_id);
CREATE INDEX idx_notebooks_created_at ON notebooks(created_at);
CREATE INDEX idx_notebooks_sort_order ON notebooks(sort_order);

-- Ensure each user has only one default notebook
CREATE UNIQUE INDEX idx_notebooks_user_default 
ON notebooks(user_id) 
WHERE is_default = TRUE AND deleted_at IS NULL;

-- =====================================================
-- 3. NOTES TABLE (Individual Notes)
-- =====================================================
CREATE TABLE notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    notebook_id UUID NOT NULL REFERENCES notebooks(id) ON DELETE CASCADE,
    title TEXT NOT NULL DEFAULT 'Untitled Note' CHECK (length(title) <= 200),
    summary TEXT CHECK (length(summary) <= 1000),
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    block_count INTEGER DEFAULT 0,
    last_viewed_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL
);

-- Add indexes for performance
CREATE INDEX idx_notes_user_id ON notes(user_id);
CREATE INDEX idx_notes_notebook_id ON notes(notebook_id);
CREATE INDEX idx_notes_created_at ON notes(created_at DESC);
CREATE INDEX idx_notes_updated_at ON notes(updated_at DESC);
CREATE INDEX idx_notes_tags ON notes USING GIN(tags);
CREATE INDEX idx_notes_is_favorite ON notes(is_favorite) WHERE is_favorite = TRUE;
CREATE INDEX idx_notes_is_pinned ON notes(is_pinned) WHERE is_pinned = TRUE;
CREATE INDEX idx_notes_title_search ON notes USING GIN(to_tsvector('english', title));

-- =====================================================
-- 4. BLOCKS TABLE (Multi-Block Content)
-- =====================================================
CREATE TYPE block_type AS ENUM (
    'TEXT',
    'AUDIO', 
    'IMAGE',
    'YOUTUBE',
    'BOOKMARK',
    'CHECKLIST'
);

CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    note_id UUID NOT NULL REFERENCES notes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    type block_type NOT NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    content JSONB NOT NULL DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL,
    
    -- Ensure sort_order is unique per note
    CONSTRAINT unique_note_sort_order UNIQUE(note_id, sort_order)
);

-- Add indexes
CREATE INDEX idx_blocks_note_id ON blocks(note_id);
CREATE INDEX idx_blocks_user_id ON blocks(user_id);
CREATE INDEX idx_blocks_type ON blocks(type);
CREATE INDEX idx_blocks_sort_order ON blocks(note_id, sort_order);
CREATE INDEX idx_blocks_created_at ON blocks(created_at);
CREATE INDEX idx_blocks_content_search ON blocks USING GIN(content);

-- Add check constraints for block content validation
ALTER TABLE blocks ADD CONSTRAINT check_text_content
CHECK (
    type != 'TEXT' OR (
        content ? 'markdown' AND
        jsonb_typeof(content->'markdown') = 'string'
    )
);

ALTER TABLE blocks ADD CONSTRAINT check_audio_content
CHECK (
    type != 'AUDIO' OR (
        content ? 'url' AND
        content ? 'duration_ms' AND
        jsonb_typeof(content->'url') = 'string' AND
        jsonb_typeof(content->'duration_ms') = 'number'
    )
);

ALTER TABLE blocks ADD CONSTRAINT check_image_content
CHECK (
    type != 'IMAGE' OR (
        content ? 'url' AND
        jsonb_typeof(content->'url') = 'string'
    )
);

-- =====================================================
-- 5. AUDIT TRIGGERS (Automatic Timestamps)
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notebooks_updated_at
    BEFORE UPDATE ON notebooks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notes_updated_at
    BEFORE UPDATE ON notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blocks_updated_at
    BEFORE UPDATE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. NOTE STATISTICS TRIGGERS
-- =====================================================
CREATE OR REPLACE FUNCTION update_note_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update block count and word count for the note
    UPDATE notes
    SET
        block_count = (
            SELECT COUNT(*)
            FROM blocks
            WHERE note_id = COALESCE(NEW.note_id, OLD.note_id)
            AND deleted_at IS NULL
        ),
        word_count = (
            SELECT COALESCE(SUM(
                CASE
                    WHEN type = 'TEXT' THEN
                        array_length(string_to_array(content->>'markdown', ' '), 1)
                    ELSE 0
                END
            ), 0)
            FROM blocks
            WHERE note_id = COALESCE(NEW.note_id, OLD.note_id)
            AND deleted_at IS NULL
        ),
        updated_at = NOW()
    WHERE id = COALESCE(NEW.note_id, OLD.note_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_note_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_note_stats();

-- =====================================================
-- 7. CLERK USER SYNC FUNCTION
-- =====================================================
CREATE OR REPLACE FUNCTION sync_clerk_user(
    clerk_user_id TEXT,
    email_address TEXT,
    first_name TEXT DEFAULT NULL,
    last_name TEXT DEFAULT NULL,
    image_url TEXT DEFAULT NULL,
    username TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    user_uuid UUID;
BEGIN
    -- Insert or update profile
    INSERT INTO profiles (
        clerk_user_id,
        email,
        first_name,
        last_name,
        avatar_url,
        username
    ) VALUES (
        clerk_user_id,
        email_address,
        first_name,
        last_name,
        image_url,
        username
    )
    ON CONFLICT (clerk_user_id)
    DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        avatar_url = EXCLUDED.avatar_url,
        username = EXCLUDED.username,
        updated_at = NOW()
    RETURNING id INTO user_uuid;

    -- Create default notebook if this is a new user
    INSERT INTO notebooks (user_id, name, is_default)
    VALUES (user_uuid, 'My Notes', true)
    ON CONFLICT DO NOTHING;

    RETURN user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
