-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE notebooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PROFILES POLICIES
-- =====================================================
-- Users can view their own profile
CREATE POLICY "Users can view their own profile" ON profiles
FOR SELECT USING (clerk_user_id = auth.jwt() ->> 'sub');

-- Users can update their own profile
CREATE POLICY "Users can update their own profile" ON profiles
FOR UPDATE USING (clerk_user_id = auth.jwt() ->> 'sub');

-- Allow profile creation (for Clerk webhook)
CREATE POLICY "Allow profile creation" ON profiles
FOR INSERT WITH CHECK (true);

-- =====================================================
-- NOTEBOOKS POLICIES
-- =====================================================
-- Users can view their own notebooks
CREATE POLICY "Users can view their own notebooks" ON notebooks
FOR SELECT USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can create their own notebooks
CREATE POLICY "Users can create their own notebooks" ON notebooks
FOR INSERT WITH CHECK (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can update their own notebooks
CREATE POLICY "Users can update their own notebooks" ON notebooks
FOR UPDATE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can delete their own notebooks
CREATE POLICY "Users can delete their own notebooks" ON notebooks
FOR DELETE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- =====================================================
-- NOTES POLICIES
-- =====================================================
-- Users can view their own notes
CREATE POLICY "Users can view their own notes" ON notes
FOR SELECT USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can create their own notes
CREATE POLICY "Users can create their own notes" ON notes
FOR INSERT WITH CHECK (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can update their own notes
CREATE POLICY "Users can update their own notes" ON notes
FOR UPDATE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can delete their own notes
CREATE POLICY "Users can delete their own notes" ON notes
FOR DELETE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- =====================================================
-- BLOCKS POLICIES
-- =====================================================
-- Users can view their own blocks
CREATE POLICY "Users can view their own blocks" ON blocks
FOR SELECT USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can create their own blocks
CREATE POLICY "Users can create their own blocks" ON blocks
FOR INSERT WITH CHECK (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can update their own blocks
CREATE POLICY "Users can update their own blocks" ON blocks
FOR UPDATE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);

-- Users can delete their own blocks
CREATE POLICY "Users can delete their own blocks" ON blocks
FOR DELETE USING (
    user_id IN (
        SELECT id FROM profiles 
        WHERE clerk_user_id = auth.jwt() ->> 'sub'
    )
);
