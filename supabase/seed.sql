-- =====================================================
-- SAMPLE TEST DATA FOR LOCAL DEVELOPMENT
-- =====================================================

-- Insert test users (simulate Clerk users)
INSERT INTO profiles (clerk_user_id, email, first_name, last_name, username) VALUES
('user_2abc123def456', '<EMAIL>', '<PERSON>', 'Doe', 'johndoe'),
('user_2xyz789ghi012', '<EMAIL>', '<PERSON>', '<PERSON>', 'jane<PERSON>'),
('user_2mno345pqr678', '<EMAIL>', '<PERSON>', '<PERSON>', 'mike<PERSON><PERSON>'),
('user_2stu901vwx234', '<EMAIL>', '<PERSON>', '<PERSON>', 'sarah<PERSON><PERSON><PERSON>'),
('user_2def567hij890', '<EMAIL>', '<PERSON>', 'Brown', 'alexbrown');

-- Create sample notebooks for first user
DO $$
DECLARE
    user_uuid UUID;
BEGIN
    SELECT id INTO user_uuid FROM profiles WHERE clerk_user_id = 'user_2abc123def456';
    
    INSERT INTO notebooks (user_id, name, description, color, is_default) VALUES
    (user_uuid, 'My Notes', 'Default notebook for all notes', '#3B82F6', true),
    (user_uuid, 'Work', 'Professional notes and meeting minutes', '#10B981', false),
    (user_uuid, 'Personal', 'Personal thoughts and ideas', '#F59E0B', false),
    (user_uuid, 'Projects', 'Project-related documentation', '#8B5CF6', false),
    (user_uuid, 'Learning', 'Study notes and research', '#EF4444', false);
END $$;

-- Create sample notes with mixed content
DO $$
DECLARE
    user_uuid UUID;
    work_notebook_uuid UUID;
    personal_notebook_uuid UUID;
    note_uuid UUID;
BEGIN
    -- Get user and notebooks
    SELECT id INTO user_uuid FROM profiles WHERE clerk_user_id = 'user_2abc123def456';
    SELECT id INTO work_notebook_uuid FROM notebooks WHERE user_id = user_uuid AND name = 'Work';
    SELECT id INTO personal_notebook_uuid FROM notebooks WHERE user_id = user_uuid AND name = 'Personal';
    
    -- Note 1: Meeting Notes with Audio
    INSERT INTO notes (user_id, notebook_id, title, tags, is_pinned)
    VALUES (user_uuid, work_notebook_uuid, 'Weekly Team Meeting - Jan 15', ARRAY['meeting', 'team', 'weekly'], true)
    RETURNING id INTO note_uuid;
    
    -- Text block
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'TEXT', 0, jsonb_build_object(
        'markdown', '# Weekly Team Meeting - January 15, 2024

## Attendees
- John Doe (Project Manager)
- Jane Smith (Backend Developer)  
- Mike Wilson (Frontend Developer)
- Sarah Johnson (UX Designer)

## Agenda
1. Project updates and progress review
2. Budget allocation for Q1
3. Sprint planning for next iteration
4. Technical challenges discussion

## Key Decisions
- Approved additional budget for audio transcription service
- Decided to implement offline mode in Phase 2
- Set deadline for MVP: February 28, 2024'
    ));
    
    -- Audio block (simulated recording)
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'AUDIO', 1, jsonb_build_object(
        'url', 'http://localhost:54321/storage/v1/object/public/audio-files/user_2abc123def456/meeting_20240115.m4a',
        'duration_ms', 1847000,
        'title', 'Team Discussion Recording',
        'transcription', 'So let me start with the project updates. We have made significant progress on the mobile app development. The authentication system is now fully integrated with Clerk, and we have completed the basic note-taking functionality. The audio recording feature is working well with expo-audio library. Jane, can you update us on the backend progress? We have successfully implemented the Supabase integration with proper RLS policies.',
        'filePath', 'audio/user_2abc123def456/meeting_20240115.m4a'
    ));
    
    -- Checklist block
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'CHECKLIST', 2, jsonb_build_object(
        'items', jsonb_build_array(
            jsonb_build_object('text', 'Review Q1 budget allocation', 'completed', true),
            jsonb_build_object('text', 'Finalize sprint backlog', 'completed', false),
            jsonb_build_object('text', 'Schedule client demo', 'completed', false),
            jsonb_build_object('text', 'Update project timeline', 'completed', true),
            jsonb_build_object('text', 'Test audio transcription workflow', 'completed', false)
        )
    ));
    
    -- Note 2: Quick Voice Note
    INSERT INTO notes (user_id, notebook_id, title, tags, is_favorite)
    VALUES (user_uuid, personal_notebook_uuid, 'Quick Idea - App Feature', ARRAY['idea', 'feature', 'voice'], true)
    RETURNING id INTO note_uuid;
    
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'AUDIO', 0, jsonb_build_object(
        'url', 'http://localhost:54321/storage/v1/object/public/audio-files/user_2abc123def456/quick_idea_20240116.m4a',
        'duration_ms', 45000,
        'title', 'Voice Note - Feature Idea',
        'transcription', 'I just had an idea for a new feature. What if we could automatically tag notes based on their content using AI? This could help users organize their notes without manual effort. We could use the transcription text and note content to suggest relevant tags.',
        'filePath', 'audio/user_2abc123def456/quick_idea_20240116.m4a'
    ));
    
    -- Note 3: Research with Mixed Content
    INSERT INTO notes (user_id, notebook_id, title, tags)
    VALUES (user_uuid, work_notebook_uuid, 'Market Research Findings', ARRAY['research', 'market', 'analysis'])
    RETURNING id INTO note_uuid;
    
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'TEXT', 0, jsonb_build_object(
        'markdown', '# Market Research Findings

Our analysis of the current note-taking app market reveals several key insights:

## Key Competitors
- **Notion**: 30M+ users, $10B valuation
- **Obsidian**: 1M+ users, growing rapidly
- **Roam Research**: 500K+ users, academic focus
- **Apple Notes**: Built-in advantage on iOS

## Market Gap
There is a clear opportunity for a mobile-first, audio-centric note-taking solution that combines:
- Voice recording with automatic transcription
- Multi-block content architecture  
- Seamless cross-platform sync
- Offline-first approach'
    )),
    (note_uuid, user_uuid, 'IMAGE', 1, jsonb_build_object(
        'url', 'http://localhost:54321/storage/v1/object/public/images/user_2abc123def456/market_analysis_chart.png',
        'caption', 'Market share analysis of top note-taking applications',
        'alt', 'Bar chart showing market share percentages for note-taking apps'
    )),
    (note_uuid, user_uuid, 'BOOKMARK', 2, jsonb_build_object(
        'url', 'https://techcrunch.com/2024/01/10/note-taking-apps-market-analysis/',
        'title', 'Note-Taking Apps Market Analysis 2024',
        'description', 'Comprehensive analysis of the note-taking application market trends and opportunities',
        'favicon', 'https://techcrunch.com/favicon.ico'
    ));
    
    -- Note 4: YouTube Research
    INSERT INTO notes (user_id, notebook_id, title, tags)
    VALUES (user_uuid, work_notebook_uuid, 'UX Design Inspiration', ARRAY['design', 'ux', 'inspiration'])
    RETURNING id INTO note_uuid;
    
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'TEXT', 0, jsonb_build_object(
        'markdown', '# UX Design Inspiration

Collecting some great examples of mobile app design patterns for our note-taking app.

## Key Takeaways
- Gesture-based navigation is crucial for mobile
- Voice input should be prominent and accessible
- Block-based editing provides flexibility
- Dark mode is essential for note-taking apps'
    )),
    (note_uuid, user_uuid, 'YOUTUBE', 1, jsonb_build_object(
        'url', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'title', 'Mobile App Design Best Practices 2024',
        'thumbnail', 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'duration', '12:34'
    ));
    
    -- Note 5: Simple Text Note
    INSERT INTO notes (user_id, notebook_id, title, tags)
    VALUES (user_uuid, personal_notebook_uuid, 'Daily Thoughts', ARRAY['journal', 'personal'])
    RETURNING id INTO note_uuid;
    
    INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
    (note_uuid, user_uuid, 'TEXT', 0, jsonb_build_object(
        'markdown', '# Daily Thoughts - January 16, 2024

Today was productive. Made good progress on the Universal Notes App. The audio recording feature is working well, and the Supabase integration is solid.

## Tomorrow''s Goals
- Test the complete audio workflow
- Implement image upload functionality  
- Add note search capabilities
- Work on the UI polish

The multi-block architecture is really paying off - it gives users so much flexibility in how they structure their content.'
    ));
    
END $$;
