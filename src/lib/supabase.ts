// =======================
// Supabase Client Factory
// =======================
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/clerk-expo';

// Environment variables
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Missing Supabase environment variables');
}

/**
 * Creates a fresh Supabase client with current Clerk authentication token
 * This ensures tokens are always current and eliminates stale token issues
 */
export const createSupabaseClient = async (session: any): Promise<SupabaseClient> => {
  if (!session) {
    throw new Error('No active session available');
  }

  try {
    // Get fresh token from Clerk session
    const token = await session.getToken({ skipCache: true });
    
    if (!token) {
      throw new Error('Failed to get authentication token from session');
    }

    // Create Supabase client with fresh token
    const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    });

    return supabaseClient;
  } catch (error) {
    console.error('[Supabase Factory] Failed to create client:', error);
    throw new Error(`Failed to create Supabase client: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Hook to get a fresh Supabase client
 * This replaces the old useSupabase hook pattern
 */
export const useSupabaseClient = () => {
  const { session } = useSession();

  const getClient = async (): Promise<SupabaseClient> => {
    return createSupabaseClient(session);
  };

  return {
    getClient,
    isAuthenticated: !!session,
    session,
  };
};

/**
 * Utility function to execute database operations with fresh client
 * This provides a clean pattern for database operations
 */
export const withSupabaseClient = async <T>(
  session: any,
  operation: (client: SupabaseClient) => Promise<T>
): Promise<T> => {
  const client = await createSupabaseClient(session);
  return operation(client);
};

/**
 * Type definitions for the new architecture
 */
export interface DatabaseOperation<T = any> {
  (client: SupabaseClient): Promise<T>;
}

export interface SupabaseClientFactory {
  getClient: () => Promise<SupabaseClient>;
  isAuthenticated: boolean;
  session: any;
}
