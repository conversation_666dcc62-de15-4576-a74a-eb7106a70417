// =======================
// Authentication Hook
// =======================
import { useState, useEffect, useCallback } from 'react';
import { useSession, useUser } from '@clerk/clerk-expo';
import { withSupabaseClient } from '@/lib/supabase';

export interface UserProfile {
  id: string;
  clerk_user_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  userProfile: UserProfile | null;
  isProfileLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

/**
 * Authentication hook that replaces the old useSupabase hook
 * Manages user profile and authentication state without global provider
 */
export const useAuth = () => {
  const { session } = useSession();
  const { user } = useUser();
  
  const [state, setState] = useState<AuthState>({
    userProfile: null,
    isProfileLoading: false,
    isAuthenticated: false,
    error: null,
  });

  /**
   * Sync user profile with Supabase
   * Creates profile if it doesn't exist, updates if it does
   */
  const syncUserProfile = useCallback(async () => {
    if (!session || !user) {
      setState(prev => ({
        ...prev,
        userProfile: null,
        isAuthenticated: false,
        isProfileLoading: false,
      }));
      return;
    }

    setState(prev => ({ ...prev, isProfileLoading: true, error: null }));

    try {
      console.log('[useAuth] 🔄 Syncing user profile for:', user.id);

      const profile = await withSupabaseClient(session, async (supabase) => {
        // First, try to get existing profile
        const { data: existingProfile, error: fetchError } = await supabase
          .from('profiles')
          .select('*')
          .eq('clerk_user_id', user.id)
          .single();

        if (existingProfile) {
          console.log('[useAuth] ✅ Found existing profile:', existingProfile.id);
          
          // Update profile with latest Clerk data
          const { data: updatedProfile, error: updateError } = await supabase
            .from('profiles')
            .update({
              email: user.primaryEmailAddress?.emailAddress || existingProfile.email,
              first_name: user.firstName || existingProfile.first_name,
              last_name: user.lastName || existingProfile.last_name,
              avatar_url: user.imageUrl || existingProfile.avatar_url,
              updated_at: new Date().toISOString(),
            })
            .eq('id', existingProfile.id)
            .select()
            .single();

          if (updateError) {
            console.error('[useAuth] ❌ Error updating profile:', updateError);
            return existingProfile; // Return existing profile if update fails
          }

          return updatedProfile;
        } else if (fetchError?.code === 'PGRST116') {
          // Profile doesn't exist, create it
          console.log('[useAuth] 📝 Creating new profile for user:', user.id);
          
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              clerk_user_id: user.id,
              email: user.primaryEmailAddress?.emailAddress || '',
              first_name: user.firstName || '',
              last_name: user.lastName || '',
              avatar_url: user.imageUrl || '',
            })
            .select()
            .single();

          if (createError) {
            throw createError;
          }

          // Create default notebook for new user
          await supabase
            .from('notebooks')
            .insert({
              user_id: newProfile.id,
              name: 'My Notes',
              description: 'Your default notebook',
              color: '#3B82F6',
              is_default: true,
            });

          console.log('[useAuth] ✅ Created new profile and default notebook');
          return newProfile;
        } else {
          throw fetchError;
        }
      });

      setState(prev => ({
        ...prev,
        userProfile: profile,
        isAuthenticated: true,
        isProfileLoading: false,
        error: null,
      }));

    } catch (error) {
      console.error('[useAuth] ❌ Error syncing profile:', error);
      setState(prev => ({
        ...prev,
        userProfile: null,
        isAuthenticated: !!session,
        isProfileLoading: false,
        error: error instanceof Error ? error.message : 'Failed to sync profile',
      }));
    }
  }, [session, user]);

  /**
   * Refresh user profile
   */
  const refreshProfile = useCallback(async () => {
    await syncUserProfile();
  }, [syncUserProfile]);

  /**
   * Validate current authentication state
   */
  const validateAuth = useCallback(async (): Promise<boolean> => {
    try {
      if (!session || !user) return false;

      const token = await session.getToken({ skipCache: true });
      return !!token;
    } catch (error) {
      console.error('[useAuth] Auth validation failed:', error);
      return false;
    }
  }, [session, user]);

  /**
   * Force sign out with cleanup
   */
  const signOut = useCallback(() => {
    setState({
      userProfile: null,
      isProfileLoading: false,
      isAuthenticated: false,
      error: null,
    });
  }, []);

  // Sync profile when session or user changes
  useEffect(() => {
    syncUserProfile();
  }, [syncUserProfile]);

  return {
    ...state,
    refreshProfile,
    validateAuth,
    signOut,
    session,
    user,
  };
};
