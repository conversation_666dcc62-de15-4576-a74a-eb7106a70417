// =======================
// Imports
// =======================
import { useState, useEffect, useCallback } from 'react';
import { Alert, Platform, Linking } from 'react-native';
import { requestRecordingPermissionsAsync, getRecordingPermissionsAsync } from 'expo-audio';
import * as Notifications from 'expo-notifications';

// =======================
// Types & Interfaces
// =======================
export interface PermissionStatus {
  microphone: {
    granted: boolean;
    canAskAgain: boolean;
    status: 'granted' | 'denied' | 'undetermined';
  };
  notifications: {
    granted: boolean;
    canAskAgain: boolean;
    status: 'granted' | 'denied' | 'undetermined';
  };
}

export interface PermissionHookReturn {
  permissions: PermissionStatus;
  isLoading: boolean;
  requestMicrophonePermission: () => Promise<boolean>;
  requestNotificationPermission: () => Promise<boolean>;
  requestAllPermissions: () => Promise<void>;
  openSettings: () => void;
  checkPermissions: () => Promise<void>;
}

// =======================
// Permission Hook
// =======================
export const usePermissions = (): PermissionHookReturn => {
  const [permissions, setPermissions] = useState<PermissionStatus>({
    microphone: {
      granted: false,
      canAskAgain: true,
      status: 'undetermined',
    },
    notifications: {
      granted: false,
      canAskAgain: true,
      status: 'undetermined',
    },
  });

  const [isLoading, setIsLoading] = useState(true);

  // =======================
  // Check Current Permissions
  // =======================
  const checkPermissions = useCallback(async () => {
    try {
      console.log('[usePermissions] Checking current permissions');

      // Check microphone permission
      const audioPermission = await getRecordingPermissionsAsync();
      
      // Check notification permission
      const notificationPermission = await Notifications.getPermissionsAsync();

      setPermissions({
        microphone: {
          granted: audioPermission.granted,
          canAskAgain: audioPermission.canAskAgain,
          status: audioPermission.granted ? 'granted' : 
                  audioPermission.canAskAgain ? 'undetermined' : 'denied',
        },
        notifications: {
          granted: notificationPermission.granted,
          canAskAgain: notificationPermission.canAskAgain,
          status: notificationPermission.granted ? 'granted' : 
                  notificationPermission.canAskAgain ? 'undetermined' : 'denied',
        },
      });

      console.log('[usePermissions] Permission status:', {
        microphone: audioPermission.granted,
        notifications: notificationPermission.granted,
      });

    } catch (error) {
      console.error('[usePermissions] Error checking permissions:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // =======================
  // Request Microphone Permission
  // =======================
  const requestMicrophonePermission = useCallback(async (): Promise<boolean> => {
    try {
      console.log('[usePermissions] Requesting microphone permission');

      const permission = await requestRecordingPermissionsAsync();
      
      const newMicrophoneStatus = {
        granted: permission.granted,
        canAskAgain: permission.canAskAgain,
        status: permission.granted ? 'granted' as const : 
                permission.canAskAgain ? 'undetermined' as const : 'denied' as const,
      };

      setPermissions(prev => ({
        ...prev,
        microphone: newMicrophoneStatus,
      }));

      if (!permission.granted) {
        console.warn('[usePermissions] Microphone permission denied');
        
        if (!permission.canAskAgain) {
          // Permission permanently denied
          Alert.alert(
            'Microphone Permission Required',
            'To record audio notes, please enable microphone access in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: openSettings },
            ]
          );
        } else {
          // Permission denied but can ask again
          Alert.alert(
            'Microphone Access Needed',
            'This app needs microphone access to record your voice notes. Would you like to grant permission?',
            [
              { text: 'Not Now', style: 'cancel' },
              { text: 'Allow', onPress: () => requestMicrophonePermission() },
            ]
          );
        }
      }

      return permission.granted;
    } catch (error) {
      console.error('[usePermissions] Error requesting microphone permission:', error);
      return false;
    }
  }, []);

  // =======================
  // Request Notification Permission
  // =======================
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    try {
      console.log('[usePermissions] Requesting notification permission');

      const permission = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: false,
        },
      });

      const newNotificationStatus = {
        granted: permission.granted,
        canAskAgain: permission.canAskAgain,
        status: permission.granted ? 'granted' as const : 
                permission.canAskAgain ? 'undetermined' as const : 'denied' as const,
      };

      setPermissions(prev => ({
        ...prev,
        notifications: newNotificationStatus,
      }));

      if (!permission.granted && !permission.canAskAgain) {
        Alert.alert(
          'Notification Permission',
          'To receive reminders and updates, please enable notifications in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: openSettings },
          ]
        );
      }

      return permission.granted;
    } catch (error) {
      console.error('[usePermissions] Error requesting notification permission:', error);
      return false;
    }
  }, []);

  // =======================
  // Request All Permissions
  // =======================
  const requestAllPermissions = useCallback(async () => {
    console.log('[usePermissions] Requesting all permissions');
    
    // Request microphone first (most critical)
    const microphoneGranted = await requestMicrophonePermission();
    
    // Request notifications (optional)
    await requestNotificationPermission();

    console.log('[usePermissions] All permission requests completed');
  }, [requestMicrophonePermission, requestNotificationPermission]);

  // =======================
  // Open Device Settings
  // =======================
  const openSettings = useCallback(() => {
    console.log('[usePermissions] Opening device settings');
    
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  }, []);

  // =======================
  // Effects
  // =======================
  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  // =======================
  // Return API
  // =======================
  return {
    permissions,
    isLoading,
    requestMicrophonePermission,
    requestNotificationPermission,
    requestAllPermissions,
    openSettings,
    checkPermissions,
  };
};
