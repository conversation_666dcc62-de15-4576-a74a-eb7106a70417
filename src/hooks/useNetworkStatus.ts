// =======================
// Imports
// =======================
import { useState, useEffect } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';

// =======================
// Types & Interfaces
// =======================
export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: string | null;
  isWifiEnabled: boolean;
  isCellularEnabled: boolean;
  isOffline: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
}

// =======================
// Network Status Hook
// =======================
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true, // Assume connected initially
    isInternetReachable: null,
    type: null,
    isWifiEnabled: false,
    isCellularEnabled: false,
    isOffline: false,
    connectionQuality: 'excellent',
  });

  const [isLoading, setIsLoading] = useState(true);

  // =======================
  // Network Quality Assessment
  // =======================
  const assessConnectionQuality = (state: NetInfoState): NetworkStatus['connectionQuality'] => {
    if (!state.isConnected || !state.isInternetReachable) {
      return 'offline';
    }

    // For WiFi connections
    if (state.type === 'wifi' && state.details && 'strength' in state.details) {
      const strength = state.details.strength;
      if (strength && strength > 80) return 'excellent';
      if (strength && strength > 50) return 'good';
      return 'poor';
    }

    // For cellular connections
    if (state.type === 'cellular' && state.details && 'cellularGeneration' in state.details) {
      const generation = state.details.cellularGeneration;
      if (generation === '5g' || generation === '4g') return 'excellent';
      if (generation === '3g') return 'good';
      return 'poor';
    }

    // Default assessment
    if (state.isInternetReachable) return 'good';
    return 'poor';
  };

  // =======================
  // Network State Handler
  // =======================
  const handleNetworkStateChange = (state: NetInfoState) => {
    console.log('[useNetworkStatus] Network state changed:', {
      isConnected: state.isConnected,
      isInternetReachable: state.isInternetReachable,
      type: state.type,
    });

    const connectionQuality = assessConnectionQuality(state);
    const isOffline = !state.isConnected || !state.isInternetReachable;

    setNetworkStatus({
      isConnected: state.isConnected ?? false,
      isInternetReachable: state.isInternetReachable,
      type: state.type,
      isWifiEnabled: state.type === 'wifi',
      isCellularEnabled: state.type === 'cellular',
      isOffline,
      connectionQuality,
    });

    setIsLoading(false);
  };

  // =======================
  // Effects
  // =======================
  useEffect(() => {
    console.log('[useNetworkStatus] Setting up network monitoring');

    // Get initial network state
    NetInfo.fetch().then(handleNetworkStateChange);

    // Subscribe to network state changes
    const unsubscribe = NetInfo.addEventListener(handleNetworkStateChange);

    return () => {
      console.log('[useNetworkStatus] Cleaning up network monitoring');
      unsubscribe();
    };
  }, []);

  // =======================
  // Utility Functions
  // =======================
  const refresh = async () => {
    console.log('[useNetworkStatus] Refreshing network status');
    const state = await NetInfo.fetch();
    handleNetworkStateChange(state);
  };

  const getConnectionMessage = (): string => {
    if (networkStatus.isOffline) {
      return 'No internet connection';
    }

    switch (networkStatus.connectionQuality) {
      case 'excellent':
        return 'Excellent connection';
      case 'good':
        return 'Good connection';
      case 'poor':
        return 'Poor connection - some features may be slow';
      default:
        return 'Connected';
    }
  };

  const shouldShowOfflineWarning = (): boolean => {
    return networkStatus.isOffline;
  };

  const shouldShowSlowConnectionWarning = (): boolean => {
    return !networkStatus.isOffline && networkStatus.connectionQuality === 'poor';
  };

  // =======================
  // Return API
  // =======================
  return {
    ...networkStatus,
    isLoading,
    refresh,
    getConnectionMessage,
    shouldShowOfflineWarning,
    shouldShowSlowConnectionWarning,
  };
};
