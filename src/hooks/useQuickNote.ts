// =======================
// Imports
// =======================
import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { QuickNoteState, QuickCreationMode, NoteDraft, BlockType, ContentBlock } from '@/types/notes';
import { useNotesService } from '@/services/notesService';
import { useBlocksService } from '@/services/blocksService';
import { useNotebooksService } from '@/services/notebooksService';


// =======================
// Initial Draft and State
// =======================
const initialDraft: NoteDraft = {
  title: '',
  notebook_id: undefined,
  tags: [],
  blocks: []
};

const initialState: QuickNoteState = {
  mode: 'audio',
  draft: initialDraft,
  isRecording: false,
  isUploading: false,
  error: undefined
};


// =======================
// useQuickNote Hook
// =======================
export const useQuickNote = () => {
  // User from Clerk
  const { user } = useUser();

  // Database services
  const notesService = useNotesService();
  const blocksService = useBlocksService();
  const notebooksService = useNotebooksService();

  // State for quick note
  const [state, setState] = useState<QuickNoteState>(initialState);


  // =======================
  // Mode Management
  // =======================
  const setMode = useCallback((mode: QuickCreationMode) => {
    setState(prev => ({ ...prev, mode }));
  }, []);


  // =======================
  // Draft Management
  // =======================
  const updateDraft = useCallback((updates: Partial<NoteDraft>) => {
    setState(prev => ({
      ...prev,
      draft: { ...prev.draft, ...updates }
    }));
  }, []);


  // =======================
  // Block Management
  // =======================
  const addBlock = useCallback((type: BlockType, content: any) => {
    const newBlock: Omit<ContentBlock, 'id' | 'note_id' | 'user_id' | 'created_at' | 'updated_at'> = {
      type,
      sort_order: state.draft.blocks.length,
      content,
      embedding: undefined,
      metadata: {},
      deleted_at: undefined
    };

    setState(prev => ({
      ...prev,
      draft: {
        ...prev.draft,
        blocks: [...prev.draft.blocks, newBlock]
      }
    }));
  }, [state.draft.blocks.length]);

  const removeBlock = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      draft: {
        ...prev.draft,
        blocks: prev.draft.blocks.filter((_, i) => i !== index)
      }
    }));
  }, []);

  const updateBlock = useCallback((index: number, content: any) => {
    setState(prev => ({
      ...prev,
      draft: {
        ...prev.draft,
        blocks: prev.draft.blocks.map((block, i) =>
          i === index ? { ...block, content } : block
        )
      }
    }));
  }, []);




  // =======================
  // Recording & Uploading State
  // =======================
  const setRecording = useCallback((isRecording: boolean) => {
    setState(prev => ({ ...prev, isRecording }));
  }, []);

  const setUploading = useCallback((isUploading: boolean) => {
    setState(prev => ({ ...prev, isUploading }));
  }, []);


  // =======================
  // Error Handling
  // =======================
  const setError = useCallback((error?: string) => {
    setState(prev => ({ ...prev, error }));
  }, []);


  // =======================
  // Reset State
  // =======================
  const reset = useCallback(() => {
    setState(initialState);
  }, []);


  // =======================
  // Tag Management
  // =======================
  const addTag = useCallback((tag: string) => {
    if (tag.trim() && !state.draft.tags.includes(tag.trim())) {
      setState(prev => ({
        ...prev,
        draft: {
          ...prev.draft,
          tags: [...prev.draft.tags, tag.trim()]
        }
      }));
    }
  }, [state.draft.tags]);

  const removeTag = useCallback((tag: string) => {
    setState(prev => ({
      ...prev,
      draft: {
        ...prev.draft,
        tags: prev.draft.tags.filter(t => t !== tag)
      }
    }));
  }, []);


  // =======================
  // Save Logic
  // =======================
  const canSave = useCallback(() => {
    // Must have either a title or at least one audio block with content
    const hasTitle = state.draft.title && state.draft.title.trim().length > 0;
    const hasAudioContent = state.draft.blocks.some(block => {
      return block.type === 'AUDIO' && block.content.url && block.content.url.length > 0;
    });

    return hasTitle || hasAudioContent;
  }, [state.draft]);

  const getValidationMessage = useCallback(() => {
    if (!canSave()) {
      return 'Please add a title or some content before saving';
    }

    // Check for title length
    if (state.draft.title && state.draft.title.length > 200) {
      return 'Title must be 200 characters or less';
    }

    // Check for too many tags
    if (state.draft.tags.length > 20) {
      return 'Maximum 20 tags allowed';
    }

    // Check for too many blocks
    if (state.draft.blocks.length > 100) {
      return 'Maximum 100 blocks allowed per note';
    }

    return null;
  }, [state.draft, canSave]);

  const saveNote = useCallback(async () => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save notes');
      return false;
    }

    const validationMessage = getValidationMessage();
    if (validationMessage) {
      Alert.alert('Validation Error', validationMessage);
      return false;
    }

    try {
      setUploading(true);
      setError(undefined);

      console.log('[useQuickNote] Starting note save process...');
      console.log('[useQuickNote] Draft data:', state.draft);

      // Ensure default notebook exists
      await notebooksService.ensureDefaultNotebook();

      // Create the note first
      const savedNote = await notesService.createNote(state.draft);
      console.log('[useQuickNote] Note created:', savedNote.id);

      // Prepare blocks data for database
      const blocksData = state.draft.blocks.map((block, index) => ({
        type: block.type,
        sort_order: index,
        content: block.content,
        metadata: block.metadata || {},
      }));

      // Save blocks if any exist
      if (blocksData.length > 0) {
        const savedBlocks = await blocksService.createBlocks(savedNote.id, blocksData);
        console.log('[useQuickNote] Blocks created:', savedBlocks.length);
      }

      // Update note with final counts (simplified for audio-only)
      await notesService.updateNote(savedNote.id, {
        word_count: 0, // Audio notes don't have word count
        block_count: state.draft.blocks.length,
      });

      console.log('[useQuickNote] Note saved successfully!');
      Alert.alert('Success', 'Note saved successfully!');
      reset();
      return true;
    } catch (error) {
      console.error('[useQuickNote] Error saving note:', error);

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to save note. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('Validation failed')) {
          errorMessage = error.message.replace('Validation failed: ', '');
        } else if (error.message.includes('User profile not available')) {
          errorMessage = 'Please wait for your profile to load and try again.';
        } else if (error.message.includes('Default notebook not found')) {
          errorMessage = 'Unable to create default notebook. Please check your connection.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      Alert.alert('Error Saving Note', errorMessage, [
        { text: 'OK', style: 'default' }
      ]);
      return false;
    } finally {
      setUploading(false);
    }
  }, [user, canSave, state.draft, reset, notesService, blocksService, notebooksService]);


  // =======================
  // Return State and Actions
  // =======================
  return {
    state,
    actions: {
      setMode,
      updateDraft,
      addBlock,
      removeBlock,
      updateBlock,

      setRecording,
      setUploading,
      setError,
      reset,
      addTag,
      removeTag,
      saveNote,
      canSave: canSave(),
      getValidationMessage
    }
  };
};
