// =======================
// Convex Voice Recorder Component
// =======================
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Ionicons } from '@expo/vector-icons';
import { useConvexAudioStorage } from '@/services/convexAudioStorage';
import { useConvexNotesService, useConvexBlocksService } from '@/services/convexNotesService';
import { useDefaultNotebook } from '@/services/convexNotebooksService';
import { Id } from '../../../convex/_generated/dataModel';

// =======================
// Types
// =======================
interface ConvexVoiceRecorderProps {
  onRecordingComplete?: (audioFileId: Id<'audioFiles'>, noteId: Id<'notes'>) => void;
  onError?: (error: string) => void;
  notebookId?: Id<'notebooks'>;
  existingNoteId?: Id<'notes'>;
  style?: any;
}

interface RecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  duration: number;
  recordingUri?: string;
}

// =======================
// Component
// =======================
export const ConvexVoiceRecorder: React.FC<ConvexVoiceRecorderProps> = ({
  onRecordingComplete,
  onError,
  notebookId,
  existingNoteId,
  style,
}) => {
  // State
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isProcessing: false,
    duration: 0,
  });

  // Refs
  const recordingRef = useRef<Audio.Recording | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Services
  const { uploadAudioFile } = useConvexAudioStorage();
  const { createNote } = useConvexNotesService();
  const { createBlock } = useConvexBlocksService();
  const defaultNotebook = useDefaultNotebook();

  // =======================
  // Effects
  // =======================
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
      if (recordingRef.current) {
        recordingRef.current.stopAndUnloadAsync();
      }
    };
  }, []);

  // =======================
  // Recording Functions
  // =======================
  const startRecording = async () => {
    try {
      console.log('[ConvexVoiceRecorder] Starting recording');

      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        const errorMsg = 'Audio recording permission is required';
        onError?.(errorMsg);
        Alert.alert('Permission Required', errorMsg);
        return;
      }

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create recording
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      recordingRef.current = recording;

      // Update state
      setRecordingState(prev => ({
        ...prev,
        isRecording: true,
        duration: 0,
      }));

      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        setRecordingState(prev => ({
          ...prev,
          duration: prev.duration + 1,
        }));
      }, 1000);

      console.log('[ConvexVoiceRecorder] Recording started');

    } catch (error) {
      console.error('[ConvexVoiceRecorder] Error starting recording:', error);
      const errorMsg = 'Failed to start recording';
      onError?.(errorMsg);
      Alert.alert('Recording Error', errorMsg);
    }
  };

  const stopRecording = async () => {
    try {
      console.log('[ConvexVoiceRecorder] Stopping recording');

      if (!recordingRef.current) {
        console.warn('[ConvexVoiceRecorder] No active recording to stop');
        return;
      }

      // Stop duration timer
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }

      // Stop recording
      await recordingRef.current.stopAndUnloadAsync();
      const uri = recordingRef.current.getURI();

      // Update state
      setRecordingState(prev => ({
        ...prev,
        isRecording: false,
        isProcessing: true,
        recordingUri: uri || undefined,
      }));

      // Process the recording
      if (uri) {
        await processRecording(uri);
      } else {
        throw new Error('No recording URI available');
      }

      recordingRef.current = null;

    } catch (error) {
      console.error('[ConvexVoiceRecorder] Error stopping recording:', error);
      const errorMsg = 'Failed to stop recording';
      onError?.(errorMsg);
      Alert.alert('Recording Error', errorMsg);
      
      // Reset state
      setRecordingState(prev => ({
        ...prev,
        isRecording: false,
        isProcessing: false,
      }));
    }
  };

  const processRecording = async (recordingUri: string) => {
    try {
      console.log('[ConvexVoiceRecorder] Processing recording:', recordingUri);

      // Determine which notebook to use
      const targetNotebookId = notebookId || defaultNotebook?._id;
      if (!targetNotebookId) {
        throw new Error('No notebook available for saving the recording');
      }

      let noteId = existingNoteId;
      let blockId: Id<'blocks'> | undefined;

      // If no existing note, create a new one
      if (!noteId) {
        const noteTitle = `Voice Note ${new Date().toLocaleString()}`;
        noteId = await createNote({
          title: noteTitle,
          notebookId: targetNotebookId,
          tags: ['voice-note'],
        });
        console.log('[ConvexVoiceRecorder] Created new note:', noteId);
      }

      // Create audio block
      blockId = await createBlock(
        noteId,
        'AUDIO',
        0, // Will be updated when we have the audio file
        {
          title: 'Voice Recording',
          duration_ms: recordingState.duration * 1000,
        }
      );
      console.log('[ConvexVoiceRecorder] Created audio block:', blockId);

      // Upload audio file
      const uploadResult = await uploadAudioFile(
        recordingUri,
        `voice_note_${Date.now()}.m4a`,
        blockId
      );

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Failed to upload audio file');
      }

      console.log('[ConvexVoiceRecorder] Audio uploaded successfully:', uploadResult.audioFileId);

      // Update block with audio file information
      if (blockId && uploadResult.storageId) {
        // The block will be automatically updated when transcription completes
        console.log('[ConvexVoiceRecorder] Audio file linked to block');
      }

      // Clean up local file
      try {
        await FileSystem.deleteAsync(recordingUri);
        console.log('[ConvexVoiceRecorder] Local recording file cleaned up');
      } catch (cleanupError) {
        console.warn('[ConvexVoiceRecorder] Failed to clean up local file:', cleanupError);
      }

      // Update state
      setRecordingState(prev => ({
        ...prev,
        isProcessing: false,
      }));

      // Notify completion
      if (uploadResult.audioFileId) {
        onRecordingComplete?.(uploadResult.audioFileId, noteId);
      }

      console.log('[ConvexVoiceRecorder] Recording processing complete');

    } catch (error) {
      console.error('[ConvexVoiceRecorder] Error processing recording:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to process recording';
      onError?.(errorMsg);
      Alert.alert('Processing Error', errorMsg);

      // Reset state
      setRecordingState(prev => ({
        ...prev,
        isProcessing: false,
      }));
    }
  };

  // =======================
  // Helper Functions
  // =======================
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getRecordingButtonColor = (): string => {
    if (recordingState.isProcessing) return '#6B7280';
    if (recordingState.isRecording) return '#EF4444';
    return '#3B82F6';
  };

  const getRecordingButtonIcon = (): string => {
    if (recordingState.isProcessing) return 'cloud-upload-outline';
    if (recordingState.isRecording) return 'stop';
    return 'mic';
  };

  // =======================
  // Render
  // =======================
  return (
    <View style={[styles.container, style]}>
      {/* Duration Display */}
      {(recordingState.isRecording || recordingState.isProcessing) && (
        <View style={styles.durationContainer}>
          <Text style={styles.durationText}>
            {recordingState.isProcessing ? 'Processing...' : formatDuration(recordingState.duration)}
          </Text>
        </View>
      )}

      {/* Recording Button */}
      <TouchableOpacity
        style={[
          styles.recordButton,
          { backgroundColor: getRecordingButtonColor() },
          recordingState.isRecording && styles.recordButtonActive,
        ]}
        onPress={recordingState.isRecording ? stopRecording : startRecording}
        disabled={recordingState.isProcessing}
        activeOpacity={0.7}
      >
        {recordingState.isProcessing ? (
          <ActivityIndicator size="large" color="white" />
        ) : (
          <Ionicons
            name={getRecordingButtonIcon() as any}
            size={32}
            color="white"
          />
        )}
      </TouchableOpacity>

      {/* Status Text */}
      <Text style={styles.statusText}>
        {recordingState.isProcessing
          ? 'Uploading and processing...'
          : recordingState.isRecording
          ? 'Recording...'
          : 'Tap to record'}
      </Text>
    </View>
  );
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  durationContainer: {
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
  },
  durationText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    fontFamily: 'monospace',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  recordButtonActive: {
    transform: [{ scale: 1.1 }],
  },
  statusText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
