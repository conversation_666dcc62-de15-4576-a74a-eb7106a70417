// =======================
// Imports
// =======================
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, TextInput, Platform, Linking } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { usePermissions } from '../../hooks/usePermissions';
import { useNetworkStatus } from '../../hooks/useNetworkStatus';
import {
  useAudioRecorder,
  useAudioPlayer,
  RecordingPresets,
  setAudioModeAsync,
  useAudioRecorderState,
  useAudioPlayerStatus,
} from 'expo-audio';
import { useAudioStorage } from '@/services/audioStorage';

// =======================
// Types & Interfaces
// =======================
interface AudioBlockEditorProps {
  block: any;
  onUpdate: (content: any) => void;
  onDelete: () => void;
  theme: any;
  autoStartRecording?: boolean;
}

// =======================
// Waveform Component
// =======================
const FAKE_WAVEFORM_DATA = [0.1,0.2,0.35,0.6,0.8,0.9,0.85,0.7,0.6,0.5,0.4,0.3,0.2,0.25,0.4,0.55,0.7,0.8,0.75,0.6,0.45,0.3,0.15,0.1,0.2,0.35,0.6,0.8,0.9,0.85,0.7,0.6,0.5,0.4,0.3,0.2,0.25,0.4,0.55,0.7,0.8,0.75,0.6,0.45,0.3,0.15,0.1,0.2,0.3,0.4,0.5,0.6,0.5,0.4,0.3,0.2,0.1];

const Waveform = ({ progress, theme }: { progress: number, theme: any }) => {
  const progressColor = theme.primary || '#3B82F6';
  const barColor = theme.secondaryText || '#A0A0A0';

  return (
    <View style={styles.waveform}>
      {FAKE_WAVEFORM_DATA.map((bar, index) => {
        const isPlayed = progress * FAKE_WAVEFORM_DATA.length > index;
        return (
          <View
            key={index}
            style={[
              styles.waveformBar,
              { 
                height: `${bar * 100}%`, 
                backgroundColor: isPlayed ? progressColor : barColor,
              }
            ]}
          />
        );
      })}
    </View>
  );
};


// =======================
// Main Component
// =======================
export const AudioBlockEditor: React.FC<AudioBlockEditorProps> = ({
  block,
  onUpdate,
  onDelete,
  theme,
  autoStartRecording = false
}) => {

  // =======================
  // Audio Content & Storage
  // =======================
  const audioContent = block.content;
  const { uploadAudioFile, transcribeAudio } = useAudioStorage();

  // =======================
  // Audio Recording State
  // =======================
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recorderState = useAudioRecorderState(audioRecorder);

  // =======================
  // Audio Playback State
  // =======================
  const audioPlayer = useAudioPlayer(audioContent.url || null);
  const playerStatus = useAudioPlayerStatus(audioPlayer);

  // =======================
  // Local Component State
  // =======================
  const [isUploading, setIsUploading] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [editingTranscription, setEditingTranscription] = useState(false);
  const [transcriptionText, setTranscriptionText] = useState(audioContent.transcription || '');

  // Hooks
  const { permissions, requestMicrophonePermission } = usePermissions();
  const { isOffline, shouldShowOfflineWarning } = useNetworkStatus();

  // =======================
  // Effects
  // =======================

  // Sync permission state with hook
  useEffect(() => {
    setPermissionGranted(permissions.microphone.granted);
  }, [permissions.microphone.granted]);

  // Request permissions on mount if not granted
  useEffect(() => {
    if (!permissions.microphone.granted && !permissions.isLoading) {
      requestPermissions();
    }
  }, [permissions.microphone.granted, permissions.isLoading]);

  // Auto start recording if needed
  useEffect(() => {
    if (autoStartRecording && permissionGranted && !audioContent.url) {
      startRecording();
    }
  }, [autoStartRecording, permissionGranted, audioContent.url]);

  // Update recording duration timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (recorderState.isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setRecordingDuration(0);
    }
    return () => clearInterval(interval);
  }, [recorderState.isRecording]);

  // =======================
  // Permission Handling
  // =======================
  const requestPermissions = async () => {
    try {
      console.log('[AudioBlockEditor] Requesting microphone permissions');

      const granted = await requestMicrophonePermission();

      if (granted) {
        console.log('[AudioBlockEditor] Microphone permission granted');
        setPermissionGranted(true);

        await setAudioModeAsync({
          playsInSilentMode: true,
          allowsRecording: true,
        });

        console.log('[AudioBlockEditor] Audio mode configured successfully');
      } else {
        console.warn('[AudioBlockEditor] Microphone permission denied');
      }
    } catch (error) {
      console.error('[AudioBlockEditor] Permission error:', error);

      Alert.alert(
        'Permission Error',
        'Failed to request microphone permission. Please check your device settings and try again.',
        [
          { text: 'OK' },
          { text: 'Retry', onPress: () => requestPermissions() }
        ]
      );
    }
  };

  // =======================
  // Recording Controls
  // =======================
  const startRecording = async () => {
    if (!permissionGranted) {
      await requestPermissions();
      return;
    }

    try {
      // Reset any previous recording state
      setRecordingDuration(0);

      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();

      console.log('[AudioBlockEditor] Recording started successfully');
    } catch (error) {
      console.error('[AudioBlockEditor] Recording error:', error);

      // Provide specific error messages
      let errorMessage = 'Failed to start recording.';
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          errorMessage = 'Microphone permission was denied. Please check your device settings.';
        } else if (error.message.includes('busy')) {
          errorMessage = 'Microphone is being used by another app. Please close other apps and try again.';
        } else if (error.message.includes('hardware')) {
          errorMessage = 'Microphone hardware error. Please restart the app and try again.';
        }
      }

      Alert.alert('Recording Error', errorMessage, [
        { text: 'OK' },
        { text: 'Retry', onPress: () => startRecording() }
      ]);
    }
  };

  const stopRecording = async () => {
    try {
      await audioRecorder.stop();
      console.log('[AudioBlockEditor] Recording stopped successfully');
      await processRecording();
    } catch (error) {
      console.error('[AudioBlockEditor] Stop recording error:', error);

      Alert.alert(
        'Recording Error',
        'Failed to stop recording properly. The recording may be corrupted.',
        [
          { text: 'OK' },
          { text: 'Try Again', onPress: () => stopRecording() }
        ]
      );
    }
  };

  // =======================
  // Recording Processing & Upload
  // =======================
  const processRecording = async () => {
    if (!audioRecorder.uri) {
      console.error('[AudioBlockEditor] No recording URI found');
      Alert.alert('Recording Error', 'No recording found. Please try recording again.');
      return;
    }

    // Check network connectivity before upload
    if (isOffline) {
      Alert.alert(
        'No Internet Connection',
        'Your recording has been saved locally. It will be uploaded when you reconnect to the internet.',
        [{ text: 'OK' }]
      );
      // TODO: Save to pending uploads queue
      return;
    }

    setIsUploading(true);

    try {
      console.log('[AudioBlockEditor] Starting upload process for:', audioRecorder.uri);

      // Upload to Supabase Storage with retry logic
      const uploadResult = await uploadAudioFile(audioRecorder.uri);

      if (!uploadResult.success) {
        console.error('[AudioBlockEditor] Upload failed:', uploadResult.error);

        // Provide specific error messages for upload failures
        let errorMessage = uploadResult.error || 'Failed to upload audio';
        if (uploadResult.error?.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (uploadResult.error?.includes('storage')) {
          errorMessage = 'Storage error. Please try again or contact support.';
        } else if (uploadResult.error?.includes('session')) {
          errorMessage = 'Session expired. Please sign in again.';
        }

        Alert.alert('Upload Failed', errorMessage, [
          { text: 'Cancel' },
          { text: 'Retry', onPress: () => processRecording() }
        ]);
        return;
      }

      console.log('[AudioBlockEditor] Upload successful:', uploadResult.publicUrl);

      // Update block with uploaded audio info
      const updatedContent = {
        ...audioContent,
        url: uploadResult.publicUrl,
        duration_ms: recordingDuration * 1000,
        title: audioContent.title || `Audio Recording ${new Date().toLocaleTimeString()}`,
        filePath: uploadResult.filePath,
      };

      onUpdate(updatedContent);

      // Start transcription (non-blocking)
      if (uploadResult.publicUrl) {
        startTranscription(uploadResult.publicUrl).catch(error => {
          console.warn('[AudioBlockEditor] Transcription failed but continuing:', error);
        });
      }
    } catch (error) {
      console.error('[AudioBlockEditor] Process recording error:', error);

      Alert.alert(
        'Processing Error',
        'Failed to process your recording. Your audio may not have been saved.',
        [
          { text: 'Cancel' },
          { text: 'Retry', onPress: () => processRecording() }
        ]
      );
    } finally {
      setIsUploading(false);
    }
  };

  // =======================
  // Transcription Handling
  // =======================
  const startTranscription = async (audioUrl: string) => {
    setIsTranscribing(true);

    try {
      console.log('[AudioBlockEditor] Starting transcription for:', audioUrl);
      const transcriptionResult = await transcribeAudio(audioUrl);

      if (transcriptionResult.success && transcriptionResult.transcription) {
        console.log('[AudioBlockEditor] Transcription successful');
        const updatedContent = {
          ...audioContent,
          transcription: transcriptionResult.transcription,
        };
        onUpdate(updatedContent);
        setTranscriptionText(transcriptionResult.transcription);
      } else {
        console.warn('[AudioBlockEditor] Transcription failed:', transcriptionResult.error);

        // Show a subtle notification that transcription failed but don't block the user
        if (transcriptionResult.error?.includes('network')) {
          console.log('[AudioBlockEditor] Network error during transcription - will retry later');
        } else if (transcriptionResult.error?.includes('api')) {
          console.log('[AudioBlockEditor] API error during transcription - service may be down');
        }
      }
    } catch (error) {
      console.error('[AudioBlockEditor] Transcription error:', error);
    } finally {
      setIsTranscribing(false);
    }
  };

  // Retry transcription manually
  const retryTranscription = async () => {
    if (audioContent.url) {
      await startTranscription(audioContent.url);
    }
  };

  // =======================
  // Playback Controls
  // =======================
  const togglePlayback = async () => {
    try {
      if (playerStatus.playing) {
        await audioPlayer.pause();
        console.log('[AudioBlockEditor] Playback paused');
      } else {
        await audioPlayer.play();
        console.log('[AudioBlockEditor] Playback started');
      }
    } catch (error) {
      console.error('[AudioBlockEditor] Playback error:', error);

      let errorMessage = 'Failed to play audio.';
      if (error instanceof Error) {
        if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('format')) {
          errorMessage = 'Audio format not supported. Please try re-recording.';
        } else if (error.message.includes('not found')) {
          errorMessage = 'Audio file not found. The file may have been deleted.';
        }
      }

      Alert.alert('Playback Error', errorMessage, [
        { text: 'OK' },
        { text: 'Retry', onPress: () => togglePlayback() }
      ]);
    }
  };

  // =======================
  // Transcription Edit Controls
  // =======================
  const saveTranscriptionEdit = () => {
    const updatedContent = {
      ...audioContent,
      transcription: transcriptionText,
    };
    onUpdate(updatedContent);
    setEditingTranscription(false);
  };

  // =======================
  // Utility Functions
  // =======================
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDuration = () => {
    if (audioContent.duration_ms) {
      return Math.round(audioContent.duration_ms / 1000);
    }
    return recordingDuration;
  };

  // =======================
  // Permission UI
  // =======================
  if (!permissionGranted) {
    return (
      <View style={[styles.container, { backgroundColor: theme.card, borderColor: theme.border }]}>
        <View style={[styles.header, { borderBottomColor: theme.border }]}>
          <View style={styles.headerLeft}>
            <Ionicons name="mic-off" size={18} color={theme.secondaryText} />
          </View>
          <TouchableOpacity onPress={onDelete} style={styles.deleteButton}>
            <Ionicons name="trash-outline" size={18} color="#EF4444" />
          </TouchableOpacity>
        </View>

        <View style={styles.permissionContainer}>
          <Ionicons name="mic-off" size={48} color={theme.secondaryText} />
          <Text style={[styles.permissionText, { color: theme.text }]}>
            Microphone permission required
          </Text>
          <TouchableOpacity
            style={[styles.permissionButton, { backgroundColor: '#3B82F6' }]}
            onPress={requestPermissions}
          >
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // =======================
  // Main Render
  // =======================
  return (
    <View style={[
      styles.container,
      { backgroundColor: theme.card, borderColor: theme.border }
    ]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <View style={styles.headerLeft}>
          <Ionicons
            name={recorderState.isRecording ? "mic" : "musical-notes"}
            size={18}
            color={recorderState.isRecording ? "#EF4444" : theme.secondaryText}
          />
          {recorderState.isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={[styles.recordingText, { color: '#EF4444' }]}>REC</Text>
            </View>
          )}
        </View>
        <TouchableOpacity onPress={onDelete} style={styles.deleteButton}>
          <Ionicons name="trash-outline" size={18} color="#EF4444" />
        </TouchableOpacity>
      </View>

      {/* Audio Content */}
      <View style={styles.audioContent}>
        {!audioContent.url && !recorderState.isRecording ? (
          // Ready to record
          <View style={styles.recordingInterface}>
            <TouchableOpacity style={styles.recordButton} onPress={startRecording}>
              <Ionicons name="mic" size={36} color="#EF4444" />
            </TouchableOpacity>
            <Text style={[styles.helperText, { color: theme.secondaryText }]}>
              Tap to record
            </Text>
          </View>
        ) : recorderState.isRecording ? (
          // Currently recording
          <View style={styles.recordingInterface}>
            <TouchableOpacity style={styles.stopButton} onPress={stopRecording}>
              <Ionicons name="stop" size={32} color="white" />
            </TouchableOpacity>
            <Text style={[styles.recordingStatus, { color: theme.text }]}>
              {formatDuration(recordingDuration)}
            </Text>
          </View>
        ) : (
          // Playback interface
          <View style={styles.playbackInterface}>
            <TouchableOpacity style={[styles.playButton, { backgroundColor: theme.primary || '#3B82F6' }]} onPress={togglePlayback}>
              <Ionicons name={playerStatus.playing ? "pause" : "play"} size={24} color="white" />
            </TouchableOpacity>
            <View style={styles.waveformContainer}>
              <Waveform progress={playerStatus.durationMillis ? (playerStatus.positionMillis / playerStatus.durationMillis) : 0} theme={theme} />
            </View>
            <Text style={[styles.durationText, { color: theme.secondaryText }]}>
              {formatDuration(getDuration())}
            </Text>
          </View>
        )}

        {(isUploading || isTranscribing) && !audioContent.url && (
          <View style={[styles.statusContainer, { borderTopColor: theme.border }]}>
            <ActivityIndicator size="small" color={theme.primary || '#3B82F6'} />
            <Text style={[styles.statusText, { color: theme.secondaryText }]}>
              {isUploading ? 'Uploading...' : 'Processing...'}
            </Text>
          </View>
        )}
      </View>

      {/* Transcription Section */}
      {(audioContent.transcription || isTranscribing || (audioContent.url && !audioContent.transcription)) && (
        <View style={[styles.transcriptionSection, { borderTopColor: theme.border }]}>
          <View style={styles.transcriptionHeader}>
            <Text style={[styles.transcriptionLabel, { color: theme.secondaryText }]}>
              Transcription
            </Text>
            <View style={styles.transcriptionActions}>
              {audioContent.transcription && !editingTranscription && (
                <TouchableOpacity
                  onPress={() => setEditingTranscription(true)}
                  style={styles.editButton}
                >
                  <Ionicons name="create-outline" size={16} color={theme.text} />
                </TouchableOpacity>
              )}
              {audioContent.url && !isTranscribing && (
                <TouchableOpacity
                  onPress={retryTranscription}
                  style={[styles.editButton, { marginLeft: 8 }]}
                >
                  <Ionicons name="refresh-outline" size={16} color={theme.text} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {isTranscribing && !audioContent.transcription ? (
            <View style={styles.transcribingContainer}>
              <ActivityIndicator size="small" color={theme.primary || '#3B82F6'} />
              <Text style={[styles.transcribingText, { color: theme.secondaryText }]}>
                Generating transcription...
              </Text>
            </View>
          ) : editingTranscription ? (
            <View style={styles.transcriptionEditContainer}>
              <TextInput
                style={[
                  styles.transcriptionInput,
                  {
                    backgroundColor: theme.background,
                    color: theme.text,
                    borderColor: theme.border,
                  }
                ]}
                value={transcriptionText}
                onChangeText={setTranscriptionText}
                multiline
                placeholder="Edit transcription..."
                placeholderTextColor={theme.secondaryText}
              />
              <View style={styles.transcriptionActions}>
                <TouchableOpacity
                  style={[styles.transcriptionActionButton, { backgroundColor: theme.border }]}
                  onPress={() => {
                    setTranscriptionText(audioContent.transcription || '');
                    setEditingTranscription(false);
                  }}
                >
                  <Text style={[styles.transcriptionActionText, { color: theme.text }]}>
                    Cancel
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.transcriptionActionButton, { backgroundColor: theme.primary || '#3B82F6' }]}
                  onPress={saveTranscriptionEdit}
                >
                  <Text style={[styles.transcriptionActionText, { color: 'white' }]}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : audioContent.transcription ? (
            <Text style={[styles.transcriptionText, { color: theme.text }]}>
              {transcriptionText}
            </Text>
          ) : (
            <View style={styles.transcriptionPlaceholder}>
              <Text style={[styles.transcriptionPlaceholderText, { color: theme.secondaryText }]}>
                Transcription not available. Tap the refresh button to try again.
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },

  // Header styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    gap: 4,
  },
  recordingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#EF4444',
  },
  recordingText: {
    fontSize: 10,
    fontWeight: '600',
  },
  deleteButton: {
    padding: 4,
  },

  // Permission UI styles
  permissionContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
    gap: 16,
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
  },
  permissionButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Audio content area
  audioContent: {
    padding: 12,
  },

  // Recording interface
  recordingInterface: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
    gap: 12,
  },
  recordButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stopButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#EF4444',
    alignItems: 'center',
    justifyContent: 'center',
  },
  helperText: {
    fontSize: 14,
  },
  recordingStatus: {
    fontSize: 20,
    fontWeight: '500',
    fontVariant: ['tabular-nums'],
  },

  // Playback interface
  playbackInterface: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    minHeight: 80,
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  waveformContainer: {
    flex: 1,
  },
  waveform: {
    height: 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 2,
    overflow: 'hidden',
  },
  waveformBar: {
    width: 3,
    borderRadius: 2,
  },
  durationText: {
    fontSize: 14,
    fontWeight: '500',
    fontVariant: ['tabular-nums'],
    marginLeft: 12,
  },

  // Status (upload/transcribe) indicator
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  statusText: {
    fontSize: 14,
  },

  // Transcription section
  transcriptionSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  transcriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transcriptionLabel: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  editButton: {
    padding: 4,
  },
  transcribingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  transcribingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  transcriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  transcriptionEditContainer: {
    marginTop: 8,
  },
  transcriptionInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    lineHeight: 20,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  transcriptionActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    gap: 8,
  },
  transcriptionActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  transcriptionActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  transcriptionPlaceholder: {
    padding: 12,
    alignItems: 'center',
  },
  transcriptionPlaceholderText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
