// =======================
// Component-Level Error Boundary
// =======================
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// =======================
// Types & Interfaces
// =======================
interface Props {
  children: ReactNode;
  componentName?: string;
  fallback?: (error: Error, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

// =======================
// Default Fallback Component
// =======================
interface DefaultFallbackProps {
  componentName?: string;
  error: Error;
  onRetry: () => void;
}

const DefaultFallback: React.FC<DefaultFallbackProps> = ({
  componentName,
  error,
  onRetry
}) => (
  <View style={styles.fallbackContainer}>
    <Ionicons name="warning-outline" size={32} color="#F59E0B" />
    <Text style={styles.fallbackTitle}>
      {componentName ? `${componentName} Error` : 'Component Error'}
    </Text>
    <Text style={styles.fallbackMessage}>
      This section couldn't load properly
    </Text>
    {__DEV__ && (
      <Text style={styles.errorDetails}>
        {error.message}
      </Text>
    )}
    <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
      <Ionicons name="refresh" size={16} color="#3B82F6" />
      <Text style={styles.retryButtonText}>Retry</Text>
    </TouchableOpacity>
  </View>
);

// =======================
// Component Error Boundary Class
// =======================
export class ComponentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const componentName = this.props.componentName || 'Unknown Component';
    
    console.error(`🔧 Component Error Boundary (${componentName}) caught an error:`, {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    console.log(`🔄 Retrying component: ${this.props.componentName || 'Unknown'}`);
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Use default fallback
      return (
        <DefaultFallback
          componentName={this.props.componentName}
          error={this.state.error}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

// =======================
// Higher-Order Component for Easy Usage
// =======================
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) => {
  const WithErrorBoundaryComponent = (props: P) => (
    <ComponentErrorBoundary componentName={componentName || WrappedComponent.name}>
      <WrappedComponent {...props} />
    </ComponentErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = `withErrorBoundary(${componentName || WrappedComponent.name})`;
  
  return WithErrorBoundaryComponent;
};

// =======================
// Hook for Error Boundary Context
// =======================
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: string) => {
    console.error('🚨 Manual error reported:', {
      error: error.message,
      stack: error.stack,
      additionalInfo: errorInfo,
      timestamp: new Date().toISOString(),
    });

    // In a real app, you would report this to your error tracking service
    // Example: Sentry.captureException(error, { extra: { additionalInfo: errorInfo } });
  };

  return { handleError };
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  fallbackContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F59E0B',
    margin: 10,
  },
  fallbackTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginTop: 8,
    marginBottom: 4,
  },
  fallbackMessage: {
    fontSize: 14,
    color: '#B45309',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 12,
    color: '#92400E',
    fontFamily: 'monospace',
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#3B82F6',
    gap: 6,
  },
  retryButtonText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500',
  },
});
