// =======================
// Error Boundary Exports
// =======================
export { GlobalErrorBoundary } from './GlobalErrorBoundary';
export { ComponentErrorBoundary, withErrorBoundary, useErrorHandler } from './ComponentErrorBoundary';

// =======================
// Error Boundary Types
// =======================
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: (error: Error, retry: () => void) => React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

// =======================
// Error Reporting Utilities
// =======================
export const reportError = (error: Error, context?: string) => {
  console.error('🚨 Error reported:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
  });

  // TODO: Integrate with crash reporting service
  // Example: Sentry.captureException(error, { tags: { context } });
};

export const reportWarning = (message: string, context?: string) => {
  console.warn('⚠️ Warning reported:', {
    message,
    context,
    timestamp: new Date().toISOString(),
  });

  // TODO: Integrate with logging service
  // Example: Sentry.captureMessage(message, 'warning', { tags: { context } });
};
