// =======================
// Global Error Boundary Component
// =======================
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

// =======================
// Types & Interfaces
// =======================
interface Props {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

// =======================
// Error Fallback UI Component
// =======================
interface ErrorFallbackProps {
  error: Error;
  errorInfo: ErrorInfo;
  errorId: string;
  onRetry: () => void;
  onReport: () => void;
}

const ErrorFallbackUI: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  errorId,
  onRetry,
  onReport
}) => (
  <SafeAreaView style={styles.container}>
    <ScrollView contentContainerStyle={styles.content}>
      {/* Error Icon */}
      <View style={styles.iconContainer}>
        <Ionicons name="warning" size={64} color="#EF4444" />
      </View>

      {/* Error Message */}
      <Text style={styles.title}>Oops! Something went wrong</Text>
      <Text style={styles.subtitle}>
        We're sorry, but the app encountered an unexpected error. 
        Don't worry - your data is safe.
      </Text>

      {/* Error Details (Development Only) */}
      {__DEV__ && (
        <View style={styles.errorDetails}>
          <Text style={styles.errorDetailsTitle}>Error Details (Dev Mode):</Text>
          <Text style={styles.errorText}>
            {error.name}: {error.message}
          </Text>
          <Text style={styles.errorStack}>
            {error.stack}
          </Text>
          <Text style={styles.errorId}>
            Error ID: {errorId}
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actions}>
        <TouchableOpacity style={styles.primaryButton} onPress={onRetry}>
          <Ionicons name="refresh" size={20} color="white" />
          <Text style={styles.primaryButtonText}>Try Again</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.secondaryButton} onPress={onReport}>
          <Ionicons name="bug" size={20} color="#3B82F6" />
          <Text style={styles.secondaryButtonText}>Report Issue</Text>
        </TouchableOpacity>
      </View>

      {/* Help Text */}
      <Text style={styles.helpText}>
        If this problem persists, please restart the app or contact support.
      </Text>
    </ScrollView>
  </SafeAreaView>
);

// =======================
// Global Error Boundary Class Component
// =======================
export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Store error info in state
    this.setState({ errorInfo });

    // Log error details
    console.error('🚨 Global Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // TODO: Send error to crash reporting service (Sentry, Crashlytics, etc.)
    this.reportErrorToCrashService(error, errorInfo);
  }

  private reportErrorToCrashService = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // TODO: Integrate with crash reporting service
      // Example: Sentry.captureException(error, { extra: errorInfo });
      
      // For now, log to console in development
      if (__DEV__) {
        console.log('📊 Error would be reported to crash service:', {
          errorId: this.state.errorId,
          error: error.message,
          componentStack: errorInfo.componentStack,
        });
      }
    } catch (reportingError) {
      console.error('Failed to report error to crash service:', reportingError);
    }
  };

  private handleRetry = () => {
    console.log('🔄 User requested retry, resetting error boundary');
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  private handleReport = () => {
    console.log('📧 User requested to report issue');
    // TODO: Open email client or feedback form
    // For now, just log the action
    if (__DEV__) {
      console.log('Would open feedback form with error details:', {
        errorId: this.state.errorId,
        error: this.state.error?.message,
      });
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(
          this.state.error,
          this.state.errorInfo!,
          this.handleRetry
        );
      }

      // Use default fallback UI
      return (
        <ErrorFallbackUI
          error={this.state.error}
          errorInfo={this.state.errorInfo!}
          errorId={this.state.errorId!}
          onRetry={this.handleRetry}
          onReport={this.handleReport}
        />
      );
    }

    return this.props.children;
  }
}

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  errorDetails: {
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  errorDetailsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#DC2626',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#DC2626',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  errorStack: {
    fontSize: 10,
    color: '#7F1D1D',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  errorId: {
    fontSize: 10,
    color: '#6B7280',
    fontFamily: 'monospace',
  },
  actions: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  secondaryButtonText: {
    color: '#3B82F6',
    fontSize: 16,
    fontWeight: '600',
  },
  helpText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
});
