// =======================
// Imports
// =======================
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../lib/theme';
import { useNetworkStatus } from '../../hooks/useNetworkStatus';

// =======================
// Types & Interfaces
// =======================
interface NetworkStatusIndicatorProps {
  showWhenOnline?: boolean;
  onPress?: () => void;
  style?: any;
}

// =======================
// Network Status Indicator Component
// =======================
export const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  showWhenOnline = false,
  onPress,
  style,
}) => {
  const { theme } = useTheme();
  const {
    isOffline,
    connectionQuality,
    getConnectionMessage,
    shouldShowOfflineWarning,
    shouldShowSlowConnectionWarning,
    refresh,
  } = useNetworkStatus();

  // Don't show anything if online and showWhenOnline is false
  if (!showWhenOnline && !shouldShowOfflineWarning() && !shouldShowSlowConnectionWarning()) {
    return null;
  }

  // =======================
  // Get Status Colors and Icons
  // =======================
  const getStatusColor = () => {
    if (isOffline) return '#EF4444'; // Red
    if (connectionQuality === 'poor') return '#F59E0B'; // Amber
    if (connectionQuality === 'good') return '#10B981'; // Green
    return '#10B981'; // Green for excellent
  };

  const getStatusIcon = () => {
    if (isOffline) return 'cloud-offline-outline';
    if (connectionQuality === 'poor') return 'wifi-outline';
    return 'wifi';
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      refresh();
    }
  };

  // =======================
  // Render
  // =======================
  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: getStatusColor(),
          borderColor: theme.border,
        },
        style,
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Ionicons
        name={getStatusIcon()}
        size={16}
        color="white"
        style={styles.icon}
      />
      <Text style={styles.text}>
        {getConnectionMessage()}
      </Text>
      {isOffline && (
        <Ionicons
          name="refresh-outline"
          size={16}
          color="white"
          style={styles.refreshIcon}
        />
      )}
    </TouchableOpacity>
  );
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginHorizontal: 16,
    marginVertical: 4,
  },
  icon: {
    marginRight: 6,
  },
  text: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  refreshIcon: {
    marginLeft: 6,
  },
});

// =======================
// Banner Version for Full Width
// =======================
interface NetworkBannerProps {
  onDismiss?: () => void;
}

export const NetworkBanner: React.FC<NetworkBannerProps> = ({ onDismiss }) => {
  const { theme } = useTheme();
  const {
    isOffline,
    connectionQuality,
    getConnectionMessage,
    shouldShowOfflineWarning,
    shouldShowSlowConnectionWarning,
    refresh,
  } = useNetworkStatus();

  // Don't show banner if connection is good
  if (!shouldShowOfflineWarning() && !shouldShowSlowConnectionWarning()) {
    return null;
  }

  const getStatusColor = () => {
    if (isOffline) return '#EF4444'; // Red
    if (connectionQuality === 'poor') return '#F59E0B'; // Amber
    return '#10B981'; // Green
  };

  const getStatusIcon = () => {
    if (isOffline) return 'cloud-offline-outline';
    if (connectionQuality === 'poor') return 'wifi-outline';
    return 'wifi';
  };

  return (
    <View
      style={[
        styles.banner,
        {
          backgroundColor: getStatusColor(),
        },
      ]}
    >
      <View style={styles.bannerContent}>
        <Ionicons
          name={getStatusIcon()}
          size={20}
          color="white"
          style={styles.bannerIcon}
        />
        <Text style={styles.bannerText}>
          {getConnectionMessage()}
        </Text>
        <View style={styles.bannerActions}>
          {isOffline && (
            <TouchableOpacity
              onPress={refresh}
              style={styles.bannerButton}
              activeOpacity={0.7}
            >
              <Ionicons name="refresh-outline" size={18} color="white" />
            </TouchableOpacity>
          )}
          {onDismiss && (
            <TouchableOpacity
              onPress={onDismiss}
              style={styles.bannerButton}
              activeOpacity={0.7}
            >
              <Ionicons name="close" size={18} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

// =======================
// Banner Styles
// =======================
const bannerStyles = StyleSheet.create({
  banner: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bannerIcon: {
    marginRight: 8,
  },
  bannerText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  bannerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bannerButton: {
    padding: 4,
    marginLeft: 8,
  },
});

// Merge styles
Object.assign(styles, bannerStyles);
