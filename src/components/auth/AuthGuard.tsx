// =======================
// Authentication Guard Component - Simplified
// =======================
import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useAuth as useClerkAuth } from '@clerk/clerk-expo';
import { Redirect, useRouter } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
  requireProfile?: boolean;
  fallbackRoute?: string;
  showLoading?: boolean;
}

/**
 * Simple AuthGuard component that protects routes
 * Simplified to avoid infinite loops and excessive logging
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireProfile = true,
  fallbackRoute = '/(app)/(public)/login',
  showLoading = true,
}) => {
  const { isSignedIn, isLoaded } = useClerkAuth();
  const { userProfile, isProfileLoading } = useAuth();
  const router = useRouter();
  const [hasChecked, setHasChecked] = useState(false);
  const [timeoutReached, setTimeoutReached] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Set up timeout for profile loading with retry mechanism
  useEffect(() => {
    if (!isLoaded || !isSignedIn || !requireProfile) return;

    // Reset timeout when profile loading starts
    if (isProfileLoading) {
      setTimeoutReached(false);
    }

    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (isProfileLoading && retryCount < 2) {
        console.log(`[AuthGuard] Profile loading timeout reached, retry ${retryCount + 1}/2`);
        setRetryCount(prev => prev + 1);
        // The useAuth hook will automatically retry when retryCount changes
      } else {
        console.log('[AuthGuard] Profile loading timeout reached, max retries exceeded');
        setTimeoutReached(true);
      }
    }, 8000); // 8 second timeout per attempt

    return () => clearTimeout(timeout);
  }, [isLoaded, isSignedIn, isProfileLoading, retryCount, requireProfile]);

  useEffect(() => {
    if (!isLoaded) return; // Wait for Clerk to load

    // If user is not signed in, redirect to login
    if (!isSignedIn) {
      console.log('[AuthGuard] User not signed in, redirecting to login');
      router.replace(fallbackRoute as any);
      return;
    }

    // If we don't require profile, just check if signed in
    if (!requireProfile && isSignedIn) {
      setHasChecked(true);
      return;
    }

    // If we require profile and it's still loading, wait (unless timeout reached)
    if (requireProfile && isProfileLoading && !timeoutReached) {
      return; // Keep waiting
    }

    // If profile loading finished and we have a profile, we're good
    if (requireProfile && !isProfileLoading && userProfile) {
      console.log('[AuthGuard] ✅ User profile loaded successfully');
      setHasChecked(true);
      <Redirect href="/(app)/(authenticated)/(tabs)/profile" />;
      return;
    }

    // If profile loading finished but no profile, or timeout reached with max retries
    if (requireProfile && (!isProfileLoading || timeoutReached) && !userProfile) {
      if (timeoutReached && retryCount >= 2) {
        console.log('[AuthGuard] ❌ User profile not available after loading/timeout with max retries, redirecting to login');
        router.replace(fallbackRoute as any);
        return;
      } else if (!isProfileLoading && !userProfile && !timeoutReached) {
        // Profile loading finished but no profile and no timeout - this might be a real auth issue
        console.log('[AuthGuard] ❌ User profile not available after loading completed, redirecting to login');
        router.replace(fallbackRoute as any);
        return;
      }
    }
  }, [isLoaded, isSignedIn, userProfile, isProfileLoading, requireProfile, router, fallbackRoute, timeoutReached]);

  // Show loading while checking authentication
  if (!isLoaded || (requireProfile && isProfileLoading && !timeoutReached) || !hasChecked) {
    if (showLoading) {
      const loadingMessage = !isLoaded
        ? 'Initializing...'
        : isProfileLoading
          ? retryCount > 0
            ? `Loading profile... (retry ${retryCount}/2)`
            : 'Loading profile...'
          : 'Checking authentication...';

      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#ffffff',
        }}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={{ color: '#000000', fontSize: 16, marginTop: 16 }}>
            {loadingMessage}
          </Text>
          {retryCount > 0 && (
            <Text style={{ color: '#666666', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
              Connection seems slow, retrying...
            </Text>
          )}
          {timeoutReached && (
            <Text style={{ color: '#ff6b6b', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
              Taking longer than expected. Please check your connection.
            </Text>
          )}
        </View>
      );
    }
    return null;
  }

  // Render protected content
  return <>{children}</>;
};

/**
 * Higher-order component for protecting screens
 */
export const withAuthGuard = <P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<AuthGuardProps, 'children'>
) => {
  return (props: P) => (
    <AuthGuard {...options}>
      <Component {...props} />
    </AuthGuard>
  );
};
