import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/hooks/useAuth';
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '~/lib/theme';

interface UserProfileStatusProps {
  showDetails?: boolean;
}

export const UserProfileStatus: React.FC<UserProfileStatusProps> = ({ 
  showDetails = false 
}) => {
  const { userProfile, isProfileLoading, refreshProfile } = useAuth();
  const { user } = useUser();
  const { theme } = useTheme();

  if (!user) {
    return (
      <View style={[styles.container, { backgroundColor: theme.card }]}>
        <Ionicons name="person-outline" size={20} color={theme.secondaryText} />
        <Text style={[styles.statusText, { color: theme.secondaryText }]}>
          Not authenticated
        </Text>
      </View>
    );
  }

  if (isProfileLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.card }]}>
        <ActivityIndicator size="small" color={theme.text} />
        <Text style={[styles.statusText, { color: theme.text }]}>
          Syncing profile...
        </Text>
      </View>
    );
  }

  if (!userProfile) {
    return (
      <View style={[styles.container, { backgroundColor: '#FEF2F2' }]}>
        <Ionicons name="warning" size={20} color="#EF4444" />
        <Text style={[styles.statusText, { color: '#EF4444' }]}>
          Profile sync failed
        </Text>
        <TouchableOpacity onPress={refreshProfile} style={styles.retryButton}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!showDetails) {
    return (
      <View style={[styles.container, { backgroundColor: '#F0FDF4' }]}>
        <Ionicons name="checkmark-circle" size={20} color="#10B981" />
        <Text style={[styles.statusText, { color: '#10B981' }]}>
          Profile synced
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.detailsContainer, { backgroundColor: theme.card }]}>
      <View style={styles.header}>
        <Ionicons name="person-circle" size={24} color={theme.text} />
        <Text style={[styles.headerText, { color: theme.text }]}>
          User Profile
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: '#10B981' }]}>
          <Text style={styles.statusBadgeText}>Synced</Text>
        </View>
      </View>
      
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.secondaryText }]}>
            Name:
          </Text>
          <Text style={[styles.detailValue, { color: theme.text }]}>
            {userProfile.first_name} {userProfile.last_name}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.secondaryText }]}>
            Email:
          </Text>
          <Text style={[styles.detailValue, { color: theme.text }]}>
            {userProfile.email}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.secondaryText }]}>
            Username:
          </Text>
          <Text style={[styles.detailValue, { color: theme.text }]}>
            {userProfile.username}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.secondaryText }]}>
            Profile ID:
          </Text>
          <Text style={[styles.detailValue, { color: theme.text, fontSize: 12 }]}>
            {userProfile.id}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity 
        onPress={refreshProfile} 
        style={[styles.refreshButton, { borderColor: theme.border }]}
      >
        <Ionicons name="refresh" size={16} color={theme.text} />
        <Text style={[styles.refreshText, { color: theme.text }]}>
          Refresh Profile
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginVertical: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  retryButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#EF4444',
    borderRadius: 4,
  },
  retryText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  detailsContainer: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  details: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 80,
  },
  detailValue: {
    fontSize: 14,
    flex: 1,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
  },
  refreshText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
});
