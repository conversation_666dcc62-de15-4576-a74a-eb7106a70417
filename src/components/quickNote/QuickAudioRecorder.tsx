// =======================
// Imports
// =======================
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';
import {
  useAudioRecorder,
  AudioModule,
  RecordingPresets,
  setAudioModeAsync,
  useAudioRecorderState,
} from 'expo-audio';

// =======================
// Types & Interfaces
// =======================
interface QuickAudioRecorderProps {
  onAudioSubmit: (audioData: { uri: string; duration: number; transcription?: string }) => void;
  onRecordingStateChange?: (isRecording: boolean) => void;
}

// =======================
// Main Component
// =======================
export const QuickAudioRecorder: React.FC<QuickAudioRecorderProps> = ({
  onAudioSubmit,
  onRecordingStateChange
}) => {

  // ----------- Theme -----------
  const { theme } = useTheme();

  // ----------- Audio Recorder Setup -----------
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recorderState = useAudioRecorderState(audioRecorder);

  // ----------- State -----------
  const [loading, setLoading] = useState(false);
  const [transcribing, setTranscribing] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [permissionGranted, setPermissionGranted] = useState(false);

  // =======================
  // Effects
  // =======================

  // Request permissions on mount
  useEffect(() => {
    requestPermissions();
  }, []);

  // Notify parent of recording state changes
  useEffect(() => {
    onRecordingStateChange?.(recorderState.isRecording);
  }, [recorderState.isRecording, onRecordingStateChange]);

  // Handle recording duration timer
  useEffect(() => {
    // Use NodeJS.Timeout for type safety in Node, but number is fine for React Native
    let interval: ReturnType<typeof setInterval>;
    if (recorderState.isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setRecordingDuration(0);
    }
    return () => clearInterval(interval);
  }, [recorderState.isRecording]);

  // =======================
  // Permission Handling
  // =======================
  const requestPermissions = async () => {
    try {
      const status = await AudioModule.requestRecordingPermissionsAsync();
      if (!status.granted) {
        Alert.alert('Permission Required', 'Microphone access is needed to record audio notes.');
        return;
      }
      
      setPermissionGranted(true);
      await setAudioModeAsync({
        playsInSilentMode: true,
        allowsRecording: true,
      });
    } catch (error) {
      console.error('Permission error:', error);
      Alert.alert('Error', 'Failed to request microphone permission.');
    }
  };

  // =======================
  // Recording Controls
  // =======================
  const startRecording = async () => {
    if (!permissionGranted) {
      await requestPermissions();
      return;
    }

    setLoading(true);
    try {
      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();
    } catch (error) {
      console.error('Recording error:', error);
      Alert.alert('Error', 'Failed to start recording.');
    } finally {
      setLoading(false);
    }
  };

  const stopRecording = async () => {
    setLoading(true);
    try {
      await audioRecorder.stop();
    } catch (error) {
      console.error('Stop recording error:', error);
      Alert.alert('Error', 'Failed to stop recording.');
    } finally {
      setLoading(false);
    }
  };

  // =======================
  // Submit Handler
  // =======================
  const handleSubmitRecording = async () => {
    if (!audioRecorder.uri) {
      Alert.alert('Error', 'No recording found.');
      return;
    }

    setTranscribing(true);
    try {
      // For now, we'll submit without transcription
      // TODO: Implement Supabase upload and Deepgram transcription
      const audioData = {
        uri: audioRecorder.uri,
        duration: recordingDuration * 1000, // Convert to milliseconds
        transcription: undefined // Will be added when we implement the full flow
      };
      
      onAudioSubmit(audioData);
    } catch (error) {
      console.error('Submit error:', error);
      Alert.alert('Error', 'Failed to process recording.');
    } finally {
      setTranscribing(false);
    }
  };

  // =======================
  // Helpers
  // =======================
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // =======================
  // Render Permission UI
  // =======================
  if (!permissionGranted) {
    return (
      <View style={styles.container}>
        <View style={[styles.permissionContainer, { backgroundColor: theme.card }]}>
          <Ionicons name="mic-off" size={48} color={theme.secondaryText} />
          <Text style={[styles.permissionText, { color: theme.text }]}>
            Microphone permission required
          </Text>
          <TouchableOpacity
            style={[styles.permissionButton, { backgroundColor: '#3B82F6' }]}
            onPress={requestPermissions}
          >
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // =======================
  // Main Render
  // =======================
  return (
    <View style={styles.container}>
      {/* Recorder UI */}
      <View style={[styles.recorderContainer, { backgroundColor: theme.card }]}>
        <TouchableOpacity
          style={[
            styles.recordButton,
            {
              backgroundColor: recorderState.isRecording ? '#EF4444' : '#3B82F6',
            }
          ]}
          onPress={recorderState.isRecording ? stopRecording : startRecording}
          disabled={loading}
          activeOpacity={0.7}
        >
          {loading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Ionicons
              name={recorderState.isRecording ? 'stop' : 'mic'}
              size={32}
              color="white"
            />
          )}
        </TouchableOpacity>

        {/* Recording Status Info */}
        <View style={styles.recordingInfo}>
          <Text style={[styles.statusText, { color: theme.text }]}>
            {recorderState.isRecording 
              ? `Recording... ${formatDuration(recordingDuration)}`
              : audioRecorder.uri 
                ? 'Recording ready'
                : 'Tap to start recording'
            }
          </Text>
          
          {/* Red "REC" indicator while recording */}
          {recorderState.isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={[styles.recordingText, { color: '#EF4444' }]}>REC</Text>
            </View>
          )}
        </View>
      </View>

      {/* Submit Button (only when recording is ready and not recording) */}
      {audioRecorder.uri && !recorderState.isRecording && (
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: '#10B981' }]}
          onPress={handleSubmitRecording}
          disabled={transcribing}
        >
          {transcribing ? (
            <ActivityIndicator color="white" />
          ) : (
            <Ionicons name="checkmark" size={20} color="white" />
          )}
          <Text style={styles.submitButtonText}>
            {transcribing ? 'Processing...' : 'Add to Note'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },

  // Permission UI styles
  permissionContainer: {
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  permissionButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Recorder UI styles
  recorderContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  recordingInfo: {
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EF4444',
    marginRight: 8,
  },
  recordingText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Submit button styles
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
