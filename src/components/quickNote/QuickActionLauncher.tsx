// =======================
// Imports
// =======================
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';
import { useRouter } from 'expo-router';


// =======================
// Types & Interfaces
// =======================
interface QuickAction {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  category: string;
  route: string;
  params?: any;
}

interface QuickActionLauncherProps {
  onClose: () => void;
}


// =======================
// Quick Actions Definition
// =======================
const quickActions: QuickAction[] = [
  // ----------- Audio Actions -----------
  {
    id: 'record-audio',
    title: 'Record Audio',
    icon: 'mic',
    category: 'Audio',
    route: '/note-editor',
    params: { startWith: 'AUDIO', mode: 'record' }
  },
  {
    id: 'upload-audio',
    title: 'Upload Audio',
    icon: 'musical-notes',
    category: 'Audio',
    route: '/note-editor',
    params: { startWith: 'AUDIO', mode: 'upload' }
  },


 
];


// =======================
// Main Component
// =======================
export const QuickActionLauncher: React.FC<QuickActionLauncherProps> = ({ onClose }) => {
  // ----------- Hooks -----------
  const { theme } = useTheme();
  const router = useRouter();

  // ----------- Handlers -----------
  const handleActionPress = (action: QuickAction) => {
    onClose(); // Close the bottom sheet first

    // Navigate to the note editor with the specified starting content type
    router.push({
      pathname: action.route as any,
      params: action.params
    });
  };

  // ----------- Group Actions by Category -----------
  const groupedActions = quickActions.reduce((groups, action) => {
    if (!groups[action.category]) {
      groups[action.category] = [];
    }
    groups[action.category].push(action);
    return groups;
  }, {} as Record<string, QuickAction[]>);

  // ----------- Get the categories in order -----------
  const categories = Object.keys(groupedActions);

  // =======================
  // Render
  // =======================
  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* ======================= */}
      {/* Header */}
      {/* ======================= */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>
          Create New Note
        </Text>
      </View>

      {/* ======================= */}
      {/* Action Groups */}
      {/* ======================= */}
      <View style={styles.content}>
        {categories.map((category, idx) => (
          <React.Fragment key={category}>
            {/* ----------- Category Section ----------- */}
            <View style={styles.categorySection}>
              <Text style={[styles.categoryTitle, { color: theme.secondaryText }]}>
                {category}
              </Text>

              {/* ----------- Actions Grid ----------- */}
              <View style={styles.actionsGrid}>
                {groupedActions[category].map((action) => (
                  <TouchableOpacity
                    key={action.id}
                    style={[
                      styles.actionButton,
                      { backgroundColor: theme.card, borderColor: theme.border }
                    ]}
                    onPress={() => handleActionPress(action)}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name={action.icon}
                      size={29}
                      color={theme.text}
                      style={styles.actionIcon}
                    />
                    <Text style={[styles.actionTitle, { color: theme.text }]}>
                      {action.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* ----------- Dashed Divider Between Groups ----------- */}
            {idx < categories.length - 1 && (
              <View
                style={[
                  styles.dashedDivider,
                  { borderColor: "white", borderStyle: "dashed" }
                ]}
              />
            )}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};


// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
    paddingBottom:23
  },

  header: {
    alignItems: 'center',
    paddingBottom: 8,
    paddingHorizontal: 20,
  },

  title: {
    fontSize: 24,
    fontWeight: '600',
    padding:8
  },

  content: {
    flex: 1,
    paddingHorizontal: 20,
  },

  categorySection: {
    marginBottom: 24,
  },

  categoryTitle: {
    fontSize: 20,
    fontWeight: '500',
    marginBottom: 12,
    padding:12
  },

  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },

  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 18,
    borderRadius: 12,
    borderWidth: 3,
    minWidth: '48%',
    flex: 1,
  },

  actionIcon: {
    marginRight: 12,
  
  },

  actionTitle: {
    fontSize: 15,
    fontWeight: '500',
    flex: 1,
  },

  dashedDivider: {
    borderBottomWidth: 3,
    borderStyle: 'dashed',
    marginVertical: 2,
    marginBottom: 0,
    opacity: 1,
    borderColor: 'red',
    color: 'white',
  },
});
