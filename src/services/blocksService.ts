// =======================
// Blocks Database Service
// =======================
import { ContentBlock, BlockType } from '@/types/notes';
import { useSupabaseClient, withSupabaseClient } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

// Block data for creation (without database-generated fields)
export interface BlockData {
  type: BlockType;
  sort_order: number;
  content: Record<string, any>;
  metadata?: Record<string, any>;
}

// Service hook for blocks operations
export const useBlocksService = () => {
  const { userProfile, session } = useAuth();
  const { getClient } = useSupabaseClient();

  // =======================
  // Create Single Block
  // =======================
  const createBlock = async (noteId: string, blockData: BlockData): Promise<ContentBlock> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: block, error } = await supabase
        .from('blocks')
        .insert({
          note_id: noteId,
          user_id: userProfile.id,
          type: blockData.type,
          sort_order: blockData.sort_order,
          content: blockData.content,
          metadata: blockData.metadata || {},
        })
        .select()
        .single();

      if (error) {
        console.error('[BlocksService] Error creating block:', error);
        throw new Error(`Failed to create block: ${error.message}`);
      }

      return block;
    });
  };

  // =======================
  // Create Multiple Blocks (Batch)
  // =======================
  const createBlocks = async (noteId: string, blocksData: BlockData[]): Promise<ContentBlock[]> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    if (blocksData.length === 0) {
      return [];
    }

    return withSupabaseClient(session, async (supabase) => {
      // Prepare blocks for insertion
      const blocksToInsert = blocksData.map(blockData => ({
        note_id: noteId,
        user_id: userProfile.id,
        type: blockData.type,
        sort_order: blockData.sort_order,
        content: blockData.content,
        metadata: blockData.metadata || {},
      }));

      const { data: blocks, error } = await supabase
        .from('blocks')
        .insert(blocksToInsert)
        .select();

      if (error) {
        console.error('[BlocksService] Error creating blocks:', error);
        throw new Error(`Failed to create blocks: ${error.message}`);
      }

      return blocks || [];
    });
  };

  // =======================
  // Update Block
  // =======================
  const updateBlock = async (blockId: string, updates: Partial<BlockData>): Promise<ContentBlock> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: block, error } = await supabase
        .from('blocks')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', blockId)
        .eq('user_id', userProfile.id) // RLS safety
        .select()
        .single();

      if (error) {
        console.error('[BlocksService] Error updating block:', error);
        throw new Error(`Failed to update block: ${error.message}`);
      }

      return block;
    });
  };

  // =======================
  // Delete Block (Soft Delete)
  // =======================
  const deleteBlock = async (blockId: string): Promise<void> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { error } = await supabase
        .from('blocks')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', blockId)
        .eq('user_id', userProfile.id);

      if (error) {
        console.error('[BlocksService] Error deleting block:', error);
        throw new Error(`Failed to delete block: ${error.message}`);
      }
    });
  };

  // =======================
  // Get Blocks for Note
  // =======================
  const getNoteBlocks = async (noteId: string): Promise<ContentBlock[]> => {
    if (!userProfile) {
      console.warn('[BlocksService] User profile not yet loaded, returning empty array');
      return [];
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: blocks, error } = await supabase
        .from('blocks')
        .select('*')
        .eq('note_id', noteId)
        .eq('user_id', userProfile.id)
        .is('deleted_at', null)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('[BlocksService] Error fetching blocks:', error);
        throw new Error(`Failed to fetch blocks: ${error.message}`);
      }

      return blocks || [];
    });
  };

  // =======================
  // Reorder Blocks
  // =======================
  const reorderBlocks = async (noteId: string, blockIds: string[]): Promise<void> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      // Update sort_order for each block
      const updates = blockIds.map((blockId, index) => ({
        id: blockId,
        sort_order: index,
        updated_at: new Date().toISOString(),
      }));

      // Use upsert to update multiple blocks
      const { error } = await supabase
        .from('blocks')
        .upsert(updates, { onConflict: 'id' });

      if (error) {
        console.error('[BlocksService] Error reordering blocks:', error);
        throw new Error(`Failed to reorder blocks: ${error.message}`);
      }
    });
  };

  // =======================
  // Replace All Blocks for Note (Used for saving drafts)
  // =======================
  const replaceNoteBlocks = async (noteId: string, blocksData: BlockData[]): Promise<ContentBlock[]> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      // Start a transaction-like operation
      // First, soft delete all existing blocks
      const { error: deleteError } = await supabase
        .from('blocks')
        .update({ deleted_at: new Date().toISOString() })
        .eq('note_id', noteId)
        .eq('user_id', userProfile.id);

      if (deleteError) {
        console.error('[BlocksService] Error deleting existing blocks:', deleteError);
        throw new Error(`Failed to delete existing blocks: ${deleteError.message}`);
      }

      // Then create new blocks
      if (blocksData.length > 0) {
        return await createBlocks(noteId, blocksData);
      }

      return [];
    });
  };

  return {
    createBlock,
    createBlocks,
    updateBlock,
    deleteBlock,
    getNoteBlocks,
    reorderBlocks,
    replaceNoteBlocks,
  };
};
