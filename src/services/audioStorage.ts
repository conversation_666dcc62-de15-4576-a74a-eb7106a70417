// =======================
// Imports
// =======================
import { useAuth } from '@/hooks/useAuth';
import { useSupabaseClient, withSupabaseClient } from '@/lib/supabase';
import { useSession } from '@clerk/clerk-expo';
import * as FileSystem from 'expo-file-system';


// =======================
// Types & Interfaces
// =======================
export interface AudioUploadResult {
  success: boolean;
  publicUrl?: string;
  filePath?: string;
  error?: string;
}

export interface TranscriptionResult {
  success: boolean;
  transcription?: string;
  error?: string;
}


// =======================
// Main Hook
// =======================
export const useAudioStorage = () => {
  // ----------- Supabase & User -----------
  const { userProfile } = useAuth();
  const { session } = useSession();
  const { getClient } = useSupabaseClient();

  // =======================
  // Upload Audio File
  // =======================
  const uploadAudioFile = async (
    localUri: string,
    fileName?: string,
    retryCount: number = 0
  ): Promise<AudioUploadResult> => {
    const maxRetries = 2;

    try {
      console.log('[uploadAudioFile] Called with:', { localUri, fileName, retryCount });

      // ----------- Check Session -----------
      if (!session) {
        console.warn('[uploadAudioFile] No active session');
        return { success: false, error: 'session: No active session. Please sign in again.' };
      }

      // ----------- Check User Profile -----------
      if (!userProfile) {
        console.warn('[uploadAudioFile] User profile not available');
        return { success: false, error: 'User profile not available. Please try again.' };
      }

      // ----------- Generate Unique Filename -----------
      const timestamp = Date.now();
      const fileExtension = localUri.split('.').pop() || 'm4a';
      const uniqueFileName = fileName || `audio_${timestamp}.${fileExtension}`;
      const filePath = `${userProfile.clerk_user_id}/${uniqueFileName}`;
      console.log('[uploadAudioFile] Generated filePath:', filePath);

      // ----------- Read File as Base64 -----------
      const fileInfo = await FileSystem.getInfoAsync(localUri);
      console.log('[uploadAudioFile] fileInfo:', fileInfo);
      if (!fileInfo.exists) {
        console.warn('[uploadAudioFile] Audio file not found at', localUri);
        return { success: false, error: 'Audio file not found. Please try recording again.' };
      }

      // Check file size (limit to 50MB)
      if (fileInfo.size && fileInfo.size > 50 * 1024 * 1024) {
        console.warn('[uploadAudioFile] File too large:', fileInfo.size);
        return { success: false, error: 'Audio file is too large. Please record a shorter audio.' };
      }

      const fileData = await FileSystem.readAsStringAsync(localUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      console.log('[uploadAudioFile] Read file as base64, length:', fileData.length);

      if (!fileData || fileData.length === 0) {
        console.warn('[uploadAudioFile] Empty file data');
        return { success: false, error: 'Audio file appears to be empty. Please try recording again.' };
      }

      // ----------- Convert Base64 to Uint8Array -----------
      const byteCharacters = atob(fileData);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      console.log('[uploadAudioFile] Converted base64 to Uint8Array, length:', byteArray.length);

      // ----------- Upload to Supabase Storage -----------
      console.log('[uploadAudioFile] Uploading to Supabase Storage:', filePath);

      const result = await withSupabaseClient(session, async (supabase) => {
        // Upload file
        const { data, error } = await supabase.storage
          .from('audio-files')
          .upload(filePath, byteArray, {
            contentType: `audio/${fileExtension}`,
            upsert: false,
          });

        if (error) {
          console.error('[uploadAudioFile] Supabase upload error:', error);
          throw new Error(error.message);
        }
        console.log('[uploadAudioFile] Upload successful:', data);

        // Get Public URL
        const { data: urlData } = supabase.storage
          .from('audio-files')
          .getPublicUrl(filePath);

        console.log('[uploadAudioFile] Public URL data:', urlData);

        if (!urlData?.publicUrl) {
          console.warn('[uploadAudioFile] Failed to get public URL');
          throw new Error('Failed to get public URL');
        }

        return {
          publicUrl: urlData.publicUrl,
          filePath: filePath,
        };
      });

      // ----------- Return Success -----------
      console.log('[uploadAudioFile] Success! Returning:', result);
      return {
        success: true,
        publicUrl: result.publicUrl,
        filePath: result.filePath,
      };
    } catch (error) {
      console.error('[uploadAudioFile] Audio upload error:', error);

      // Retry logic for network errors
      if (retryCount < maxRetries && error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('timeout') || error.message.includes('fetch')) {
          console.log(`[uploadAudioFile] Retrying upload (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
          return uploadAudioFile(localUri, fileName, retryCount + 1);
        }
      }

      // Categorize errors for better user feedback
      let errorMessage = 'Unknown error occurred during upload.';
      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'network: Network connection failed. Please check your internet connection.';
        } else if (error.message.includes('storage') || error.message.includes('bucket')) {
          errorMessage = 'storage: Storage service error. Please try again later.';
        } else if (error.message.includes('session') || error.message.includes('auth')) {
          errorMessage = 'session: Authentication error. Please sign in again.';
        } else if (error.message.includes('size') || error.message.includes('large')) {
          errorMessage = 'File size error. Please record a shorter audio.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  };


  // =======================
  // Transcribe Audio
  // =======================
  const transcribeAudio = async (publicUrl: string, retryCount: number = 0): Promise<TranscriptionResult> => {
    const maxRetries = 2;

    try {
      console.log('[transcribeAudio] Called with publicUrl:', publicUrl, 'retryCount:', retryCount);

      // ----------- Validate URL -----------
      if (!publicUrl || !publicUrl.startsWith('http')) {
        console.warn('[transcribeAudio] Invalid audio URL:', publicUrl);
        return { success: false, error: 'Invalid audio URL provided' };
      }

      // ----------- Get Deepgram API Key -----------
      const deepgramApiKey = process.env.EXPO_PUBLIC_DEEPGRAM_API_KEY;
      console.log('[transcribeAudio] Deepgram API Key:', deepgramApiKey ? 'Present' : 'Missing');

      if (!deepgramApiKey) {
        console.warn('[transcribeAudio] Deepgram API key not configured');
        return { success: false, error: 'api: Transcription service not configured' };
      }

      // ----------- Call Deepgram API -----------
      const requestBody = {
        url: publicUrl,
        model: 'nova-2',
        smart_format: true,
        punctuate: true,
        diarize: false,
      };
      console.log('[transcribeAudio] Sending request to Deepgram:', requestBody);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch('https://api.deepgram.com/v1/listen', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${deepgramApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // ----------- Handle API Error -----------
      if (!response.ok) {
        const errorText = await response.text();
        console.error('[transcribeAudio] Deepgram API error:', response.status, errorText);

        // Retry on certain status codes
        if ((response.status === 429 || response.status >= 500) && retryCount < maxRetries) {
          console.log(`[transcribeAudio] Retrying transcription (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1))); // Exponential backoff
          return transcribeAudio(publicUrl, retryCount + 1);
        }

        return { success: false, error: `api: Transcription service error (${response.status})` };
      }

      // ----------- Parse Transcription -----------
      const result = await response.json();
      console.log('[transcribeAudio] Deepgram response:', result);
      const transcription = result.results?.channels?.[0]?.alternatives?.[0]?.transcript;

      if (!transcription) {
        console.warn('[transcribeAudio] No transcription found in response');
        return { success: false, error: 'No transcription found in response' };
      }

      // ----------- Return Success -----------
      console.log('[transcribeAudio] Success! Transcription:', transcription.trim());
      return {
        success: true,
        transcription: transcription.trim(),
      };
    } catch (error) {
      console.error('[transcribeAudio] Transcription error:', error);

      // Retry on network errors
      if (retryCount < maxRetries && error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('[transcribeAudio] Request timed out, retrying...');
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          console.log(`[transcribeAudio] Network error, retrying (${retryCount + 1}/${maxRetries})`);
        } else {
          // Don't retry for other errors
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown transcription error',
          };
        }

        await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1))); // Exponential backoff
        return transcribeAudio(publicUrl, retryCount + 1);
      }

      // Categorize errors for better user feedback
      let errorMessage = 'Unknown transcription error';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'network: Transcription request timed out. Please try again.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'network: Network error during transcription. Please check your connection.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  };


  // =======================
  // Delete Audio File
  // =======================
  const deleteAudioFile = async (filePath: string): Promise<boolean> => {
    try {
      console.log('[deleteAudioFile] Called with filePath:', filePath);

      // ----------- Check Session -----------
      if (!session) {
        console.warn('[deleteAudioFile] No active session');
        return false;
      }

      // ----------- Remove File -----------
      await withSupabaseClient(session, async (supabase) => {
        const { error } = await supabase.storage
          .from('audio-files')
          .remove([filePath]);

        if (error) {
          console.error('[deleteAudioFile] Delete error:', error);
          throw new Error(error.message);
        }
      });

      // ----------- Return Success -----------
      console.log('[deleteAudioFile] File deleted successfully:', filePath);
      return true;
    } catch (error) {
      console.error('[deleteAudioFile] Delete audio file error:', error);
      return false;
    }
  };


  // =======================
  // Return API
  // =======================
  return {
    uploadAudioFile,
    transcribeAudio,
    deleteAudioFile,
  };
};
