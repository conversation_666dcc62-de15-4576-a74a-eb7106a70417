// =======================
// Notebooks Database Service
// =======================
import { Notebook } from '@/types/notes';
import { useSupabaseClient, withSupabaseClient } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

// Notebook data for creation
export interface NotebookData {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  is_default?: boolean;
  sort_order?: number;
  metadata?: Record<string, any>;
}

// Service hook for notebooks operations
export const useNotebooksService = () => {
  const { userProfile, session } = useAuth();
  const { getClient } = useSupabaseClient();

  // =======================
  // Create Notebook
  // =======================
  const createNotebook = async (notebookData: NotebookData): Promise<Notebook> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      // If this is set as default, unset other defaults first
      if (notebookData.is_default) {
        await supabase
          .from('notebooks')
          .update({ is_default: false })
          .eq('user_id', userProfile.id)
          .eq('is_default', true);
      }

      const { data: notebook, error } = await supabase
        .from('notebooks')
        .insert({
          user_id: userProfile.id,
          name: notebookData.name,
          description: notebookData.description,
          color: notebookData.color || '#3B82F6',
          icon: notebookData.icon || 'book',
          is_default: notebookData.is_default || false,
          sort_order: notebookData.sort_order || 0,
          metadata: notebookData.metadata || {},
        })
        .select()
        .single();

      if (error) {
        console.error('[NotebooksService] Error creating notebook:', error);
        throw new Error(`Failed to create notebook: ${error.message}`);
      }

      return notebook;
    });
  };

  // =======================
  // Update Notebook
  // =======================
  const updateNotebook = async (notebookId: string, updates: Partial<NotebookData>): Promise<Notebook> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      // If setting as default, unset other defaults first
      if (updates.is_default) {
        await supabase
          .from('notebooks')
          .update({ is_default: false })
          .eq('user_id', userProfile.id)
          .eq('is_default', true)
          .neq('id', notebookId);
      }

      const { data: notebook, error } = await supabase
        .from('notebooks')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', notebookId)
        .eq('user_id', userProfile.id) // RLS safety
        .select()
        .single();

      if (error) {
        console.error('[NotebooksService] Error updating notebook:', error);
        throw new Error(`Failed to update notebook: ${error.message}`);
      }

      return notebook;
    });
  };

  // =======================
  // Delete Notebook (Soft Delete)
  // =======================
  const deleteNotebook = async (notebookId: string): Promise<void> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      // Check if this is the default notebook
      const { data: notebook } = await supabase
        .from('notebooks')
        .select('is_default')
        .eq('id', notebookId)
        .eq('user_id', userProfile.id)
        .single();

      if (notebook?.is_default) {
        throw new Error('Cannot delete the default notebook');
      }

      const { error } = await supabase
        .from('notebooks')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', notebookId)
        .eq('user_id', userProfile.id);

      if (error) {
        console.error('[NotebooksService] Error deleting notebook:', error);
        throw new Error(`Failed to delete notebook: ${error.message}`);
      }
    });
  };

  // =======================
  // Get User Notebooks
  // =======================
  const getUserNotebooks = async (): Promise<Notebook[]> => {
    if (!userProfile) {
      console.warn('[NotebooksService] User profile not yet loaded, returning empty array');
      return [];
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: notebooks, error } = await supabase
        .from('notebooks')
        .select('*')
        .eq('user_id', userProfile.id)
        .is('deleted_at', null)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('[NotebooksService] Error fetching notebooks:', error);
        throw new Error(`Failed to fetch notebooks: ${error.message}`);
      }

      return notebooks || [];
    });
  };

  // =======================
  // Get Default Notebook
  // =======================
  const getDefaultNotebook = async (): Promise<Notebook | null> => {
    if (!userProfile) {
      console.warn('[NotebooksService] User profile not yet loaded, cannot fetch default notebook');
      return null;
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: notebook, error } = await supabase
        .from('notebooks')
        .select('*')
        .eq('user_id', userProfile.id)
        .eq('is_default', true)
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No default notebook found
        }
        console.error('[NotebooksService] Error fetching default notebook:', error);
        throw new Error(`Failed to fetch default notebook: ${error.message}`);
      }

      return notebook;
    });
  };

  // =======================
  // Ensure Default Notebook Exists
  // =======================
  const ensureDefaultNotebook = async (): Promise<Notebook> => {
    let defaultNotebook = await getDefaultNotebook();
    
    if (!defaultNotebook) {
      // Create default notebook
      defaultNotebook = await createNotebook({
        name: 'My Notes',
        description: 'Default notebook for your notes',
        color: '#3B82F6',
        icon: 'book',
        is_default: true,
        sort_order: 0,
      });
    }

    return defaultNotebook;
  };

  // =======================
  // Get Single Notebook
  // =======================
  const getNotebook = async (notebookId: string): Promise<Notebook | null> => {
    if (!userProfile) {
      console.warn('[NotebooksService] User profile not yet loaded, cannot fetch notebook');
      return null;
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: notebook, error } = await supabase
        .from('notebooks')
        .select('*')
        .eq('id', notebookId)
        .eq('user_id', userProfile.id)
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Notebook not found
        }
        console.error('[NotebooksService] Error fetching notebook:', error);
        throw new Error(`Failed to fetch notebook: ${error.message}`);
      }

      return notebook;
    });
  };

  return {
    createNotebook,
    updateNotebook,
    deleteNotebook,
    getUserNotebooks,
    getDefaultNotebook,
    ensureDefaultNotebook,
    getNotebook,
  };
};
