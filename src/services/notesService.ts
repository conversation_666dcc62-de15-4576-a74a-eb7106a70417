// =======================
// Notes Database Service
// =======================
import { Note, NoteDraft } from '@/types/notes';
import { withSupabaseClient } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

// Filter types for querying notes
export interface NoteFilters {
  notebookId?: string;
  tags?: string[];
  search?: string;
  isFavorite?: boolean;
  isArchived?: boolean;
  isPinned?: boolean;
}

// Validation functions
const validateNoteData = (noteData: NoteDraft): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Title validation
  if (noteData.title && noteData.title.length > 200) {
    errors.push('Title must be 200 characters or less');
  }

  // Tags validation
  if (noteData.tags.length > 20) {
    errors.push('Maximum 20 tags allowed');
  }

  noteData.tags.forEach(tag => {
    if (tag.length > 50) {
      errors.push('Each tag must be 50 characters or less');
    }
  });

  // Blocks validation
  if (noteData.blocks.length > 100) {
    errors.push('Maximum 100 blocks allowed per note');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Service hook for notes operations
export const useNotesService = () => {
  const { userProfile, session } = useAuth();

  // =======================
  // Create Note
  // =======================
  const createNote = async (noteData: NoteDraft): Promise<Note> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    // Validate note data
    const validation = validateNoteData(noteData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    console.log('[NotesService] 🚀 Creating note...');

    return withSupabaseClient(session, async (supabase) => {
      // Get default notebook if none specified
      let notebookId = noteData.notebook_id;
      if (!notebookId) {
        const { data: defaultNotebook, error: notebookError } = await supabase
          .from('notebooks')
          .select('id')
          .eq('user_id', userProfile.id)
          .eq('is_default', true)
          .single();

        if (notebookError || !defaultNotebook) {
          throw new Error('Default notebook not found');
        }
        notebookId = defaultNotebook.id;
      }

      // Calculate word count from blocks (for audio blocks, use transcription if available)
      const wordCount = noteData.blocks.reduce((total, block) => {
        if (block.type === 'AUDIO' && block.content && typeof block.content === 'object' && 'transcription' in block.content && block.content.transcription) {
          return total + String(block.content.transcription).split(/\s+/).length;
        }
        return total;
      }, 0);

      // Create the note
      const { data: note, error } = await supabase
        .from('notes')
        .insert({
          user_id: userProfile.id,
          notebook_id: notebookId,
          title: noteData.title || 'Untitled Note',
          tags: noteData.tags || [],
          word_count: wordCount,
          block_count: noteData.blocks.length,
          metadata: {},
        })
        .select()
        .single();

      if (error) {
        console.error('[NotesService] Error creating note:', error);
        throw new Error(`Failed to create note: ${error.message}`);
      }

      console.log('[NotesService] ✅ Note created successfully:', note.id);
      return note;
    });
  };

  // =======================
  // Update Note
  // =======================
  const updateNote = async (noteId: string, updates: Partial<Note>): Promise<Note> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: note, error } = await supabase
        .from('notes')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', noteId)
        .eq('user_id', userProfile.id) // RLS safety
        .select()
        .single();

      if (error) {
        console.error('[NotesService] Error updating note:', error);
        throw new Error(`Failed to update note: ${error.message}`);
      }

      return note;
    });
  };

  // =======================
  // Delete Note (Soft Delete)
  // =======================
  const deleteNote = async (noteId: string): Promise<void> => {
    if (!userProfile) {
      throw new Error('User profile not available');
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { error } = await supabase
        .from('notes')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', noteId)
        .eq('user_id', userProfile.id);

      if (error) {
        console.error('[NotesService] Error deleting note:', error);
        throw new Error(`Failed to delete note: ${error.message}`);
      }
    });
  };

  // =======================
  // Get User Notes
  // =======================
  const getUserNotes = async (filters?: NoteFilters): Promise<Note[]> => {
    if (!userProfile) {
      console.warn('[NotesService] User profile not yet loaded, returning empty array');
      return [];
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      let query = supabase
        .from('notes')
        .select(`
          *,
          notebook:notebooks(id, name, color, icon),
          blocks(id, type, sort_order, content, metadata)
        `)
        .eq('user_id', userProfile.id)
        .is('deleted_at', null)
        .order('updated_at', { ascending: false });

      // Apply filters
      if (filters?.notebookId) {
        query = query.eq('notebook_id', filters.notebookId);
      }

      if (filters?.tags?.length) {
        query = query.overlaps('tags', filters.tags);
      }

      if (filters?.search) {
        query = query.textSearch('title', filters.search);
      }

      if (filters?.isFavorite) {
        query = query.eq('is_favorite', true);
      }

      if (filters?.isArchived !== undefined) {
        query = query.eq('is_archived', filters.isArchived);
      }

      if (filters?.isPinned) {
        query = query.eq('is_pinned', true);
      }

      const { data: notes, error } = await query;

      if (error) {
        console.error('[NotesService] Error fetching notes:', error);
        throw new Error(`Failed to fetch notes: ${error.message}`);
      }

      return notes || [];
    });
  };

  // =======================
  // Get Single Note
  // =======================
  const getNote = async (noteId: string): Promise<Note | null> => {
    if (!userProfile) {
      console.warn('[NotesService] User profile not yet loaded, cannot fetch note');
      return null;
    }

    if (!session) {
      throw new Error('No active session');
    }

    return withSupabaseClient(session, async (supabase) => {
      const { data: note, error } = await supabase
        .from('notes')
        .select(`
          *,
          notebook:notebooks(id, name, color, icon),
          blocks(id, type, sort_order, content, metadata, created_at, updated_at)
        `)
        .eq('id', noteId)
        .eq('user_id', userProfile.id)
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Note not found
        }
        console.error('[NotesService] Error fetching note:', error);
        throw new Error(`Failed to fetch note: ${error.message}`);
      }

      // Sort blocks by sort_order
      if (note.blocks) {
        note.blocks.sort((a: any, b: any) => a.sort_order - b.sort_order);
      }

      return note;
    });
  };

  return {
    createNote,
    updateNote,
    deleteNote,
    getUserNotes,
    getNote,
  };
};
