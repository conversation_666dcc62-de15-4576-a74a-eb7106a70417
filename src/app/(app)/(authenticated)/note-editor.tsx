// =======================
// Imports
// =======================
import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';
import { useQuickNote } from '@/hooks/useQuickNote';
import { useAuth } from '@/hooks/useAuth';



// --- Block Editor Components ---
import { AudioBlockEditor } from '@/components/blocks';



// =======================
// Main Component
// =======================
export default function NoteEditorScreen() {
  // --- Hooks and State ---
  const { theme } = useTheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  const { state, actions } = useQuickNote();
  const { userProfile, isProfileLoading } = useAuth();


  // =======================
  // Effects
  // =======================

  // Initialize with audio block if none exists
  useEffect(() => {
    console.log('[NoteEditorScreen] useEffect - blocks.length:', state.draft.blocks.length);
    if (state.draft.blocks.length === 0) {
      console.log('[NoteEditorScreen] Adding initial AUDIO block');
      actions.addBlock('AUDIO', {
        url: '',
        duration_ms: 0,
        transcription: '',
        title: '',
      });
    }
  }, [state.draft.blocks.length]);

  // =======================
  // Handlers
  // =======================

  // Handle Save Note
  const handleSave = async () => {
    console.log('[NoteEditorScreen] handleSave called');

    if (!userProfile) {
      console.log('[NoteEditorScreen] No userProfile, cannot save');
      Alert.alert(
        'Profile Not Ready',
        'Your user profile is still being set up. Please wait a moment and try again.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (!actions.canSave) {
      console.log('[NoteEditorScreen] actions.canSave is false');
      Alert.alert(
        'Empty Note',
        'Please add some audio content before saving your note.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Show confirmation if there's unsaved content
    if (state.draft.blocks.length > 0) {
      Alert.alert(
        'Save Note',
        'Are you ready to save your audio note?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Save',
            onPress: async () => {
              console.log('[NoteEditorScreen] Attempting to save note...');
              const success = await actions.saveNote();
              console.log('[NoteEditorScreen] Save result:', success);
              if (success) {
                router.back();
              }
            }
          }
        ]
      );
    }
  };





  // =======================
  // Block Management Handlers (simplified for single audio block)
  // =======================
  const handleUpdateBlock = (content: any) => {
    console.log('[NoteEditorScreen] handleUpdateBlock content:', content);
    if (state.draft.blocks.length > 0) {
      actions.updateBlock(0, content);
    }
  };

  // =======================
  // Render
  // =======================
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* =======================
          Header
      ======================= */}
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={() => {
          console.log('[NoteEditorScreen] Close button pressed');
          router.back();
        }} style={styles.headerButton}>
          <Ionicons name="close" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          New Note
        </Text>
        
        <TouchableOpacity
          onPress={handleSave}
          style={[
            styles.saveButton,
            {
              backgroundColor: (actions.canSave && userProfile) ? '#3B82F6' : theme.border,
              opacity: (state.isUploading || isProfileLoading) ? 0.7 : 1
            }
          ]}
          disabled={!actions.canSave || state.isUploading || !userProfile || isProfileLoading}
        >
          <Text style={[
            styles.saveButtonText,
            { color: (actions.canSave && userProfile) ? 'white' : theme.secondaryText }
          ]}>
            {state.isUploading ? 'Saving...' :
             isProfileLoading ? 'Loading...' :
             !userProfile ? 'Profile Loading...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* =======================
          Note Content
      ======================= */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* --- Title Input --- */}
        <View style={styles.titleSection}>
          <Text style={[styles.titlePlaceholder, { color: theme.secondaryText }]}>
            Title (optional)
          </Text>
          {/* We'll add a proper title input component later */}
        </View>

        {/* --- Audio Block --- */}
        <View style={styles.blocksContainer}>
          {state.draft.blocks.length === 0 ? (
            /* --- Empty state --- */
            <View style={[styles.emptyState, { backgroundColor: theme.card }]}>
              <Ionicons name="mic-outline" size={48} color={theme.secondaryText} />
              <Text style={[styles.emptyStateText, { color: theme.text }]}>
                Start recording your audio note
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.secondaryText }]}>
                Tap the microphone to begin
              </Text>
            </View>
          ) : (
            /* --- Single Audio Block --- */
            <AudioBlockEditor
              block={state.draft.blocks[0]}
              onUpdate={handleUpdateBlock}
              onDelete={() => {}}
              theme={theme}
              autoStartRecording={params.startWith === 'AUDIO'}
            />
          )}
        </View>
      </ScrollView>



    </SafeAreaView>
  );
}

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // --- Header Styles ---
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  // --- Content Styles ---
  content: {
    flex: 1,
  },
  titleSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  titlePlaceholder: {
    fontSize: 14,
    fontWeight: '500',
  },

  // --- Blocks Styles ---
  blocksContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 100, // Space for add button
  },
  blockContainer: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },

  // --- Empty State Styles ---
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginTop: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },

});
