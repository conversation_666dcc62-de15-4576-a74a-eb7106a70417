import React, { useEffect } from 'react';
import { View, StatusBar, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import OnboardingScreen from '~/components/onboarding/onboard';
import * as NavigationBar from 'expo-navigation-bar';

export default function Onboarding() {
  useEffect(() => {
    if (Platform.OS === 'android') {
      NavigationBar.setStyle('dark');
    }
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar backgroundColor="#121212" hidden={false} />
        <OnboardingScreen />
      </SafeAreaView>
    </View>
  );
}

