import { Ionicons } from '@expo/vector-icons';

export type TabConfig = {
  name: string;
  icon?: keyof typeof Ionicons.glyphMap;
  activeIcon?: keyof typeof Ionicons.glyphMap;
  label?: string;
  isCenter?: boolean;
};

export const TABS: TabConfig[] = [
  { name: 'index', icon: 'home-outline', activeIcon: 'home', label: 'Home' },
  
  { name: 'center', isCenter: true },
  
  { name: 'profile', icon: 'person-outline', activeIcon: 'person', label: 'Profile' },
];
