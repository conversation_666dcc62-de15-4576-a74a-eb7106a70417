// =======================
// Imports
// =======================
import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Switch,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useUser, useClerk } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { useTheme } from '~/lib/theme';
import { useAuth } from '@/hooks/useAuth';
import { useNotesService } from '@/services/notesService';

// =======================
// Types
// =======================
interface ProfileStats {
  totalNotes: number;
  totalWords: number;
  totalBlocks: number;
  pinnedNotes: number;
  favoriteNotes: number;
}

// =======================
// Components
// =======================
interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  theme: any;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  theme
}) => (
  <TouchableOpacity
    style={[styles.settingItem, { backgroundColor: theme.card, borderColor: theme.border }]}
    onPress={onPress}
    disabled={!onPress}
  >
    <View style={styles.settingLeft}>
      <View style={[styles.settingIcon, { backgroundColor: '#3B82F6' }]}>
        <Ionicons name={icon as any} size={20} color="white" />
      </View>
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color: theme.text }]}>{title}</Text>
        {subtitle && (
          <Text style={[styles.settingSubtitle, { color: theme.secondaryText }]}>
            {subtitle}
          </Text>
        )}
      </View>
    </View>
    <View style={styles.settingRight}>
      {rightElement || (
        onPress && <Ionicons name="chevron-forward" size={20} color={theme.secondaryText} />
      )}
    </View>
  </TouchableOpacity>
);

interface StatCardProps {
  icon: string;
  title: string;
  value: string | number;
  color: string;
  theme: any;
}

const StatCard: React.FC<StatCardProps> = ({ icon, title, value, color, theme }) => (
  <View style={[styles.statCard, { backgroundColor: theme.card, borderColor: theme.border }]}>
    <View style={[styles.statIcon, { backgroundColor: color }]}>
      <Ionicons name={icon as any} size={24} color="white" />
    </View>
    <Text style={[styles.statValue, { color: theme.text }]}>{value}</Text>
    <Text style={[styles.statTitle, { color: theme.secondaryText }]}>{title}</Text>
  </View>
);

// =======================
// Main Profile Screen Component
// =======================
const ProfileScreen = () => {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const { theme } = useTheme();
  const { userProfile, isProfileLoading } = useAuth();
  const notesService = useNotesService();

  // State
  const [stats, setStats] = useState<ProfileStats>({
    totalNotes: 0,
    totalWords: 0,
    totalBlocks: 0,
    pinnedNotes: 0,
    favoriteNotes: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  // =======================
  // Data Loading
  // =======================
  const loadStats = async () => {
    try {
      const notes = await notesService.getUserNotes();

      const newStats: ProfileStats = {
        totalNotes: notes.length,
        totalWords: notes.reduce((sum, note) => sum + note.word_count, 0),
        totalBlocks: notes.reduce((sum, note) => sum + note.block_count, 0),
        pinnedNotes: notes.filter(note => note.is_pinned).length,
        favoriteNotes: notes.filter(note => note.is_favorite).length,
      };

      setStats(newStats);
    } catch (error) {
      console.error('Error loading stats:', error);
      // Show error alert only if not refreshing (to avoid interrupting user)
      if (!isRefreshing) {
        Alert.alert('Error', 'Failed to load profile statistics');
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    // Only load stats when user profile is available
    if (userProfile && !isProfileLoading) {
      loadStats();
    }
  }, [userProfile, isProfileLoading]);

  // =======================
  // Handlers
  // =======================
  const handleRefresh = () => {
    setIsRefreshing(true);
    loadStats();
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              router.replace('/');
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const handleExportData = () => {
    Alert.alert(
      'Export Data',
      'Export all your notes and data',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => {
          // TODO: Implement data export
          Alert.alert('Coming Soon', 'Data export feature will be available soon');
        }},
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your notes and data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Coming Soon', 'Account deletion will be available soon');
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor="#3B82F6"
            colors={["#3B82F6"]}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: theme.text }]}>Profile</Text>
        </View>

        {/* User Info */}
        <View style={[styles.userSection, { backgroundColor: theme.card, borderColor: theme.border }]}>
          <Image
            source={{ uri: user?.imageUrl }}
            style={styles.avatar}
          />
          <View style={styles.userInfo}>
            <Text style={[styles.userName, { color: theme.text }]}>
              {user?.firstName} {user?.lastName}
            </Text>
            <Text style={[styles.userEmail, { color: theme.secondaryText }]}>
              {user?.emailAddresses[0]?.emailAddress}
            </Text>
            <View style={styles.userBadge}>
              <Ionicons name="checkmark-circle" size={16} color="#10B981" />
              <Text style={[styles.badgeText, { color: '#10B981' }]}>Verified</Text>
            </View>
          </View>
        </View>

        {/* Stats */}
        <View style={styles.statsSection}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Your Statistics</Text>
          <View style={styles.statsGrid}>
            <StatCard
              icon="document-text"
              title="Total Notes"
              value={stats.totalNotes}
              color="#3B82F6"
              theme={theme}
            />
            <StatCard
              icon="text"
              title="Words Written"
              value={stats.totalWords.toLocaleString()}
              color="#10B981"
              theme={theme}
            />
            <StatCard
              icon="layers"
              title="Blocks Created"
              value={stats.totalBlocks}
              color="#F59E0B"
              theme={theme}
            />
            <StatCard
              icon="pin"
              title="Pinned Notes"
              value={stats.pinnedNotes}
              color="#EF4444"
              theme={theme}
            />
          </View>
        </View>

        {/* Settings */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Settings</Text>

          <SettingItem
            icon="notifications"
            title="Notifications"
            subtitle="Get notified about important updates"
            rightElement={
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: theme.border, true: '#3B82F6' }}
                thumbColor={notificationsEnabled ? 'white' : theme.secondaryText}
              />
            }
            theme={theme}
          />

          <SettingItem
            icon="save"
            title="Auto-save"
            subtitle="Automatically save changes"
            rightElement={
              <Switch
                value={autoSaveEnabled}
                onValueChange={setAutoSaveEnabled}
                trackColor={{ false: theme.border, true: '#3B82F6' }}
                thumbColor={autoSaveEnabled ? 'white' : theme.secondaryText}
              />
            }
            theme={theme}
          />

          <SettingItem
            icon="download"
            title="Export Data"
            subtitle="Download all your notes and data"
            onPress={handleExportData}
            theme={theme}
          />

          <SettingItem
            icon="help-circle"
            title="Help & Support"
            subtitle="Get help and contact support"
            onPress={() => Alert.alert('Coming Soon', 'Help & Support will be available soon')}
            theme={theme}
          />

          <SettingItem
            icon="information-circle"
            title="About"
            subtitle="App version and information"
            onPress={() => Alert.alert('Universal Notes', 'Version 1.0.0\nBuilt with ❤️ for productivity')}
            theme={theme}
          />
        </View>

        {/* Account Actions */}
        <View style={styles.accountSection}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Account</Text>

          <SettingItem
            icon="log-out"
            title="Sign Out"
            subtitle="Sign out of your account"
            onPress={handleSignOut}
            theme={theme}
          />

          <SettingItem
            icon="trash"
            title="Delete Account"
            subtitle="Permanently delete your account and data"
            onPress={handleDeleteAccount}
            theme={theme}
          />
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.secondaryText }]}>
            Universal Notes v1.0.0
          </Text>
          <Text style={[styles.footerText, { color: theme.secondaryText }]}>
            Made with ❤️ for productivity
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    marginBottom: 8,
  },
  userBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statsSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    gap: 8,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statTitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  settingsSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  accountSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
  },
  settingRight: {
    marginLeft: 12,
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 24,
    gap: 4,
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
  },
});