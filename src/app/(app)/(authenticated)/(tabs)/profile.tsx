import React from 'react';
import { View, ScrollView, TouchableOpacity, Image, StyleSheet, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useUser } from '@clerk/clerk-expo';

const USER_STATS = [
  { value: '14', label: 'Day Streak', icon: 'flame' as const, color: ['#FF9966', '#FF5E62'], shadow: 'shadow-orange-300/50', size: 'large' },
  { value: '256', label: 'Words Learned', icon: 'book' as const, color: ['#4FACFE', '#00F2FE'], shadow: 'shadow-blue-300/50', size: 'small' },
  { value: '87%', label: 'Mastery', icon: 'trophy' as const, color: ['#B465DA', '#CF6CC9'], shadow: 'shadow-purple-300/50', size: 'small' },
];

const ACHIEVEMENTS = [
  { id: 1, name: 'First Steps', icon: 'footsteps', unlocked: true, color: '#6DBEFF', gradient: ['#4FACFE', '#00F2FE'] },
  { id: 2, name: 'Word Collector', icon: 'briefcase', unlocked: true, color: '#FFB26B', gradient: ['#FF9966', '#FF5E62'] },
  { id: 3, name: 'Perfect Lesson', icon: 'sparkles', unlocked: true, color: '#A5DD9B', gradient: ['#84FAB0', '#8FD3F4'] },
  { id: 4, name: 'Streak Starter', icon: 'rocket', unlocked: true, color: '#D087F2', gradient: ['#B465DA', '#CF6CC9'] },
  { id: 5, name: 'Weekend Warrior', icon: 'calendar', unlocked: false },
  { id: 6, name: 'Night Owl', icon: 'moon', unlocked: false },
  { id: 7, name: 'Early Bird', icon: 'sunny', unlocked: false },
  { id: 8, name: 'Legendary', icon: 'medal', unlocked: false },
];

export default function Profile2Screen() {
  const { user } = useUser();
  return (
    <SafeAreaView edges={['top']} className="flex-1 bg-white">
      <LinearGradient
        colors={['#F9FAFB', '#F3F4F6']}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
      />
      

      <View className="flex-row items-center justify-between px-5 pt-8">
         <View className="flex-row items-center">
           <Image
        source={{ uri: user?.imageUrl }}
        className="w-14 h-14 rounded-full mr-4"
           />
           <View>
        <Text className="text-lg font-semibold text-gray-800">
          Hi, <Text className="font-bold">{user?.firstName || 'User'}</Text>
        </Text>
        <Text className="text-sm text-gray-500">
          Welcome back to your notes
        </Text>
           </View>
         </View>
         <TouchableOpacity className="p-2 rounded-full bg-white/80 shadow">
           <Ionicons name="notifications-outline" size={24} color="#374151" />
         </TouchableOpacity>
       </View>
      {/* --- SETTINGS ICON --- */}
      <TouchableOpacity 
        className="absolute top-16 right-5 z-10 p-2 bg-white/80 rounded-full shadow-sm"
        style={styles.settingsButton}
      >
        <Ionicons name="settings-outline" size={24} color="#6B7280" />
      </TouchableOpacity>
        
      <ScrollView 
        showsVerticalScrollIndicator={false} 
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        
        {/* --- HEADER --- */}
        <Animated.View 
          className="items-center pt-14 pb-8"
          entering={FadeInDown.duration(600).delay(100)}
        >
          <View className="w-32 h-32 rounded-full bg-white p-1.5" style={styles.profileImageContainer}>
           
            <View className="absolute bottom-0 right-0 bg-green-500 w-6 h-6 rounded-full border-2 border-white items-center justify-center">
              <View className="w-2 h-2 rounded-full bg-white" />
            </View>
          </View>
          <Text style={{ fontWeight: 'bold', fontSize: 28, color: '#374151', marginTop: 20 }}>
            Riyan
          </Text>
          <View className="rounded-full px-4 py-1.5 mt-3" style={styles.badgeContainer}>
            <LinearGradient
              colors={['rgba(249, 115, 22, 0.2)', 'rgba(251, 146, 60, 0.2)']}
              style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, borderRadius: 9999 }}
            />
            <Text style={{ fontSize: 14, color: '#EA580C', fontWeight: '600' }}>
              Word Explorer
            </Text>
          </View>
        </Animated.View>
 
        {/* --- STATS CAROUSEL --- */}
        <Animated.View entering={FadeInDown.duration(600).delay(200)}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false} 
            contentContainerStyle={{ paddingHorizontal: 20, paddingVertical: 10, paddingRight: 40, alignItems: 'center' }}
            decelerationRate="fast"
            snapToInterval={200}
          >
            {USER_STATS.map((stat, index) => (
              <View 
                key={stat.label} 
                className={`rounded-3xl p-5 mr-5 ${stat.size === 'large' ? 'w-52 h-60' : 'w-44 h-52'}`} 
                style={[
                  styles.statCard,
                  { marginLeft: index === 0 ? 0 : 5 }
                ]}
              >
               
                <View className="flex-1 justify-between">
                  <Ionicons 
                    name={stat.icon} 
                    size={stat.size === 'large' ? 110 : 90} 
                    color="white" 
                    style={{ position: 'absolute', top: -20, right: -25, opacity: 0.2 }} 
                  />
                  <View/>
                  <View>
                    <Text style={{
                      color: 'white',
                      fontWeight: '800',
                      fontSize: stat.size === 'large' ? 48 : 40,
                    }}>
                      {stat.value}
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: 16, marginTop: 4, letterSpacing: 1 }}>
                      {stat.label}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        </Animated.View>

        {/* --- ACHIEVEMENTS SHOWCASE --- */}
        
          <View className="flex-row items-center justify-between mb-6">
            <Text style={{ fontWeight: 'bold', fontSize: 22, color: '#374151' }}>
              Achievements
            </Text>
            <TouchableOpacity className="flex-row items-center"></TouchableOpacity>
              <Text style={{ fontSize: 14, color: '#6366F1', marginRight: 4 }}>View All</Text>
              <Ionicons name="chevron-forward" size={16} color="#6366F1" />
          
          </View>
          
          <View className="flex-row flex-wrap justify-start -mx-2">
            {ACHIEVEMENTS.map((badge, index) => (
              <View 
                key={badge.id} 
                className="items-center mb-6 w-1/4 px-2"
                style={{ transform: [{ translateY: index % 2 !== 0 ? 20 : 0 }] }}
              >
                <View className="relative">
                  {badge.unlocked ? (
                    <View style={styles.achievementBadge}>
                    
                      <Ionicons name={badge.icon as any} size={34} color="white" style={{ zIndex: 1 }} />
                    </View>
                  ) : (
                    <View 
                      className="w-[76px] h-[76px] rounded-full items-center justify-center bg-gray-100"
                      style={styles.lockedBadge}
                    >
                      <Ionicons name="lock-closed" size={28} color="#BDBDBD" />
                      <View className="absolute inset-0 rounded-full bg-black/5" />
                    </View>
                  )}
                  
                  {badge.unlocked && (
                    <View className="absolute -top-1 -right-1 bg-yellow-400 w-5 h-5 rounded-full border-2 border-white items-center justify-center">
                      <Ionicons name="checkmark" size={12} color="white" />
                    </View>
                  )}
                </View>
                <Text style={{ fontSize: 12, textAlign: 'center', color: '#4B5563', marginTop: 12, fontWeight: '500' }} numberOfLines={2}>
                  {badge.name}
                </Text>
              </View>
            ))}
          </View>
      
        
      
          <Text style={{ fontWeight: 'bold', fontSize: 20, color: '#374151', marginBottom: 16 }}>
            Learning Stats
          </Text>
          
          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View className="w-10 h-10 rounded-full bg-blue-100 items-center justify-center mr-3"></View>
                <Ionicons name="time-outline" size={20} color="#3B82F6" />
              </View>
              <Text style={{ color: '#374151' }}>Time Spent</Text>
            </View>
            <Text style={{ fontWeight: '500', fontSize: 18, color: '#374151' }}>
              14.5 hours
            </Text>
         
          
          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View className="w-10 h-10 rounded-full bg-green-100 items-center justify-center mr-3">
                <Ionicons name="checkmark-circle-outline" size={20} color="#10B981" />
              </View>
              <Text style={{ color: '#374151' }}>Lessons Completed</Text>
            </View>
            <Text style={{ fontWeight: '500', fontSize: 18, color: '#374151' }}>
              42
            </Text>
          </View>
          
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <View className="w-10 h-10 rounded-full bg-purple-100 items-center justify-center mr-3"></View>
                <Ionicons name="star-outline" size={20} color="#8B5CF6" />
              </View>
              <Text style={{ color: '#374151' }}>Total XP</Text>
            </View>
            <Text style={{ fontWeight: '500', fontSize: 18, color: '#374151' }}>
              3,450
            </Text>
            
       
        
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  settingsButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  profileImageContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  profileImage: {
    borderWidth: 3,
    borderColor: 'white',
  },
  badgeContainer: {
    shadowColor: '#F97316',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
  },
  achievementBadge: {
    width: 76,
    height: 76,
    borderRadius: 38,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  lockedBadge: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statsContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  }
});