// =======================
// Imports
// =======================
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '~/lib/theme';
import { useNotesService, NoteFilters } from '@/services/notesService';
import { Note } from '@/types/notes';
import { useAuth } from '@/hooks/useAuth';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { NetworkBanner } from '@/components/ui/NetworkStatusIndicator';
import { OfflineStorageManager } from '@/utils/offlineStorage';

// =======================
// Types
// =======================
type SortOption = 'updated' | 'created' | 'title' | 'wordCount';
type ViewMode = 'list' | 'grid';

// =======================
// Components
// =======================
interface NoteListItemProps {
  note: Note;
  onPress: () => void;
  onLongPress: () => void;
  theme: any;
  viewMode: ViewMode;
}

const NoteListItem: React.FC<NoteListItemProps> = ({
  note,
  onPress,
  onLongPress,
  theme,
  viewMode
}) => {
  const getBlockTypeIcon = (type: string) => {
    switch (type) {
      case 'TEXT': return 'document-text';
      case 'AUDIO': return 'mic';
      case 'IMAGE': return 'image';
      case 'CHECKLIST': return 'checkbox';
      case 'YOUTUBE': return 'logo-youtube';
      case 'BOOKMARK': return 'link';
      default: return 'document';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const containerStyle = viewMode === 'grid' ? styles.gridItem : styles.listItem;

  return (
    <TouchableOpacity
      style={[containerStyle, { backgroundColor: theme.card, borderColor: theme.border }]}
      onPress={onPress}
      onLongPress={onLongPress}
    >
      <View style={styles.noteHeader}>
        <View style={styles.noteTitleRow}>
          {note.is_pinned && (
            <Ionicons name="pin" size={14} color="#F59E0B" style={styles.pinIcon} />
          )}
          {note.is_favorite && (
            <Ionicons name="heart" size={14} color="#EF4444" style={styles.favoriteIcon} />
          )}
          <Text style={[styles.noteTitle, { color: theme.text }]} numberOfLines={2}>
            {note.title}
          </Text>
        </View>
        <Text style={[styles.noteDate, { color: theme.secondaryText }]}>
          {formatDate(note.updated_at)}
        </Text>
      </View>

      <View style={styles.noteContent}>
        <View style={styles.noteStats}>
          <View style={styles.statItem}>
            <Ionicons name="layers" size={12} color={theme.secondaryText} />
            <Text style={[styles.statText, { color: theme.secondaryText }]}>
              {note.block_count}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="text" size={12} color={theme.secondaryText} />
            <Text style={[styles.statText, { color: theme.secondaryText }]}>
              {note.word_count}
            </Text>
          </View>
        </View>

        {note.blocks && note.blocks.length > 0 && (
          <View style={styles.blockTypes}>
            {note.blocks.slice(0, 5).map((block, index) => (
              <Ionicons
                key={index}
                name={getBlockTypeIcon(block.type) as any}
                size={14}
                color={theme.secondaryText}
                style={styles.blockTypeIcon}
              />
            ))}
            {note.blocks.length > 5 && (
              <Text style={[styles.moreBlocks, { color: theme.secondaryText }]}>
                +{note.blocks.length - 5}
              </Text>
            )}
          </View>
        )}

        {note.tags && note.tags.length > 0 && (
          <View style={styles.tags}>
            {note.tags.slice(0, 3).map((tag, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: theme.background }]}>
                <Text style={[styles.tagText, { color: theme.secondaryText }]}>
                  {tag}
                </Text>
              </View>
            ))}
            {note.tags.length > 3 && (
              <Text style={[styles.moreTags, { color: theme.secondaryText }]}>
                +{note.tags.length - 3}
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

// =======================
// Main All Notes Screen Component
// =======================
const AllNotesScreen = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const { userProfile, isProfileLoading } = useAuth();
  const notesService = useNotesService();
  const { isOffline, shouldShowOfflineWarning } = useNetworkStatus();

  // State
  const [notes, setNotes] = useState<Note[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('updated');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pinned' | 'favorites'>('all');

  // =======================
  // Data Loading
  // =======================
  const loadNotes = async () => {
    try {
      const filters: NoteFilters = {};

      if (selectedFilter === 'pinned') {
        filters.isPinned = true;
      } else if (selectedFilter === 'favorites') {
        filters.isFavorite = true;
      }

      const allNotes = await notesService.getUserNotes(filters);
      setNotes(allNotes);
      applyFiltersAndSort(allNotes, searchQuery, sortBy);
    } catch (error) {
      console.error('Error loading notes:', error);
      Alert.alert('Error', 'Failed to load notes');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // =======================
  // Offline Data Loading
  // =======================
  const loadOfflineNotes = async () => {
    try {
      setIsLoading(true);
      console.log('[AllNotesScreen] Loading cached notes for offline mode');

      const cachedNotes = await OfflineStorageManager.getCachedNotes();

      if (cachedNotes.length > 0) {
        // Convert cached notes to Note format
        const formattedNotes: Note[] = cachedNotes.map(cached => ({
          id: cached.id,
          title: cached.title,
          content: cached.content || '',
          created_at: cached.created_at,
          updated_at: cached.updated_at,
          user_id: cached.user_id,
          notebook_id: cached.notebook_id,
          is_pinned: cached.is_pinned,
          is_favorite: cached.is_favorite,
          is_archived: false,
          tags: [],
          word_count: 0,
          view_count: 0,
          block_count: 0,
          blocks: [],
          metadata: {},
        }));

        setNotes(formattedNotes);
        console.log(`[AllNotesScreen] Loaded ${formattedNotes.length} cached notes`);
      } else {
        console.log('[AllNotesScreen] No cached notes available');
      }
    } catch (error) {
      console.error('[AllNotesScreen] Error loading offline notes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotesAndCache = async () => {
    try {
      await loadNotes();

      // Cache the loaded notes for offline access
      if (notes.length > 0) {
        await OfflineStorageManager.cacheNotes(notes);
        console.log('[AllNotesScreen] Notes cached for offline access');
      }
    } catch (error) {
      console.error('[AllNotesScreen] Error loading and caching notes:', error);
    }
  };

  // =======================
  // Filtering and Sorting
  // =======================
  const applyFiltersAndSort = (notesList: Note[], query: string, sort: SortOption) => {
    let filtered = notesList;

    // Apply search filter
    if (query.trim()) {
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(query.toLowerCase()) ||
        note.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sort) {
        case 'updated':
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        case 'created':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'wordCount':
          return b.word_count - a.word_count;
        default:
          return 0;
      }
    });

    setFilteredNotes(filtered);
  };

  useEffect(() => {
    // Load notes when user profile is available
    if (userProfile && !isProfileLoading) {
      if (isOffline) {
        // Load cached notes when offline
        loadOfflineNotes();
      } else {
        // Load fresh notes when online and cache them
        loadNotesAndCache();
      }
    }
  }, [selectedFilter, userProfile, isProfileLoading, isOffline]);

  useEffect(() => {
    applyFiltersAndSort(notes, searchQuery, sortBy);
  }, [notes, searchQuery, sortBy]);

  // =======================
  // Handlers
  // =======================
  const handleRefresh = () => {
    setIsRefreshing(true);
    loadNotes();
  };

  const handleNotePress = (note: Note) => {
    router.push(`/note-editor?noteId=${note.id}`);
  };

  const handleNoteLongPress = (note: Note) => {
    Alert.alert(
      note.title,
      'Choose an action',
      [
        { text: 'Edit', onPress: () => handleNotePress(note) },
        { text: 'Pin', onPress: () => togglePin(note) },
        { text: 'Favorite', onPress: () => toggleFavorite(note) },
        { text: 'Delete', onPress: () => deleteNote(note), style: 'destructive' },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const togglePin = async (note: Note) => {
    try {
      await notesService.updateNote(note.id, { is_pinned: !note.is_pinned });
      loadNotes();
    } catch (error) {
      Alert.alert('Error', 'Failed to update note');
    }
  };

  const toggleFavorite = async (note: Note) => {
    try {
      await notesService.updateNote(note.id, { is_favorite: !note.is_favorite });
      loadNotes();
    } catch (error) {
      Alert.alert('Error', 'Failed to update note');
    }
  };

  const deleteNote = async (note: Note) => {
    Alert.alert(
      'Delete Note',
      `Are you sure you want to delete "${note.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await notesService.deleteNote(note.id);
              loadNotes();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete note');
            }
          },
        },
      ]
    );
  };

  const createNewNote = () => {
    router.push('/note-editor');
  };

  if (isLoading || isProfileLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading notes...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Network Status Banner */}
      {shouldShowOfflineWarning() && <NetworkBanner />}

      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.text }]}>All Notes</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
          >
            <Ionicons
              name={viewMode === 'list' ? 'grid-outline' : 'list-outline'}
              size={24}
              color={theme.text}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton} onPress={createNewNote}>
            <Ionicons name="add" size={24} color={theme.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: theme.card, borderColor: theme.border }]}>
          <Ionicons name="search" size={20} color={theme.secondaryText} />
          <TextInput
            style={[styles.searchInput, { color: theme.text }]}
            placeholder="Search notes..."
            placeholderTextColor={theme.secondaryText}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.secondaryText} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        {[
          { key: 'all', label: 'All', count: notes.length },
          { key: 'pinned', label: 'Pinned', count: notes.filter(n => n.is_pinned).length },
          { key: 'favorites', label: 'Favorites', count: notes.filter(n => n.is_favorite).length },
        ].map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterTab,
              { backgroundColor: theme.card, borderColor: theme.border },
              selectedFilter === filter.key && { backgroundColor: '#3B82F6' }
            ]}
            onPress={() => setSelectedFilter(filter.key as any)}
          >
            <Text
              style={[
                styles.filterTabText,
                { color: selectedFilter === filter.key ? 'white' : theme.text }
              ]}
            >
              {filter.label} ({filter.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <Text style={[styles.sortLabel, { color: theme.secondaryText }]}>Sort by:</Text>
        <View style={styles.sortOptions}>
          {[
            { key: 'updated', label: 'Updated' },
            { key: 'created', label: 'Created' },
            { key: 'title', label: 'Title' },
            { key: 'wordCount', label: 'Length' },
          ].map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.sortOption,
                { borderColor: theme.border },
                sortBy === option.key && { backgroundColor: '#3B82F6' }
              ]}
              onPress={() => setSortBy(option.key as SortOption)}
            >
              <Text
                style={[
                  styles.sortOptionText,
                  { color: sortBy === option.key ? 'white' : theme.text }
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Notes List */}
      {filteredNotes.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="document-outline" size={64} color={theme.secondaryText} />
          <Text style={[styles.emptyStateText, { color: theme.text }]}>
            {searchQuery ? 'No notes found' : 'No notes yet'}
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: theme.secondaryText }]}>
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Create your first note to get started'
            }
          </Text>
          {!searchQuery && (
            <TouchableOpacity style={styles.createButton} onPress={createNewNote}>
              <Text style={styles.createButtonText}>Create Note</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={filteredNotes}
          renderItem={({ item }) => (
            <NoteListItem
              note={item}
              onPress={() => handleNotePress(item)}
              onLongPress={() => handleNoteLongPress(item)}
              theme={theme}
              viewMode={viewMode}
            />
          )}
          keyExtractor={(item) => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.notesList}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

export default AllNotesScreen;

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 8,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 12,
  },
  sortLabel: {
    fontSize: 14,
  },
  sortOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  sortOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  sortOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  notesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  listItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  gridItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    marginHorizontal: 6,
  },
  noteHeader: {
    marginBottom: 12,
  },
  noteTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  pinIcon: {
    marginRight: 4,
  },
  favoriteIcon: {
    marginRight: 4,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  noteDate: {
    fontSize: 12,
  },
  noteContent: {
    gap: 8,
  },
  noteStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
  },
  blockTypes: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  blockTypeIcon: {
    marginRight: 2,
  },
  moreBlocks: {
    fontSize: 11,
  },
  tags: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  tagText: {
    fontSize: 11,
  },
  moreTags: {
    fontSize: 11,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    gap: 16,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  createButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});