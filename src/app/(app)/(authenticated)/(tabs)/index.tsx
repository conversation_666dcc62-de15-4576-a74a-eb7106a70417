import { Button, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { useRouter } from 'expo-router';
import { useOnboardingState } from '~/lib/onboardingState';
import { useAuth } from '@clerk/clerk-expo';
import { useAppStatus } from '~/lib/AppStatusProvider';

const IndexScreen = () => {
  const { resetOnboarding } = useOnboardingState();
  const { signOut } = useAuth();
  const router = useRouter();
  const { isOnline, microphonePermission } = useAppStatus();

  const handleReset = async () => {
    await resetOnboarding();
    router.replace('/(app)/(authenticated)/onboard');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Main App</Text>
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>Network Status: {isOnline ? 'Online' : 'Offline'}</Text>
        <Text style={styles.statusText}>Microphone Permission: {microphonePermission}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <Button title="Reset Onboarding" onPress={handleReset} color="#4F8EF7" />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="Sign Out" onPress={() => signOut()} color="#D9534F" />
      </View>
    </View>
  );
};

export default IndexScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 32,
    color: '#222',
  },
  statusContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  buttonContainer: {
    width: '80%',
    marginBottom: 16,
  },
});