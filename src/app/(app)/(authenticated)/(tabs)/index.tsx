// =======================
// Imports
// =======================
import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  ScrollView, 
  RefreshControl,
  ActivityIndicator,
  Alert,
  Image
} from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Link, useRouter } from 'expo-router';
import { useTheme } from '~/lib/theme';
import { useNotesService } from '@/services/notesService';
import { Note } from '@/types/notes';
import { UserProfileStatus } from '@/components/auth/UserProfileStatus';
import { useAuth } from '@/hooks/useAuth';

// =======================
// Components
// =======================
interface NoteCardProps {
  note: Note;
  onPress: () => void;
  theme: any;
}

const NoteCard: React.FC<NoteCardProps> = ({ note, onPress, theme }) => {
  const getBlockTypeIcon = (type: string) => {
    switch (type) {
      case 'TEXT': return 'document-text';
      case 'AUDIO': return 'mic';
      case 'IMAGE': return 'image';
      case 'CHECKLIST': return 'checkbox';
      case 'YOUTUBE': return 'logo-youtube';
      case 'BOOKMARK': return 'link';
      default: return 'document';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <TouchableOpacity
      style={[styles.noteCard, { backgroundColor: theme.card, borderColor: theme.border }]}
      onPress={onPress}
    >
      <View style={styles.noteCardHeader}>
        <View style={styles.noteCardTitle}>
          {note.is_pinned && (
            <Ionicons name="pin" size={14} color="#F59E0B" style={styles.pinIcon} />
          )}
          <Text style={[styles.noteTitle, { color: theme.text }]} numberOfLines={1}>
            {note.title}
          </Text>
        </View>
        <Text style={[styles.noteDate, { color: theme.secondaryText }]}>
          {formatDate(note.updated_at)}
        </Text>
      </View>

      <View style={styles.noteCardContent}>
        <View style={styles.noteStats}>
          <View style={styles.statItem}>
            <Ionicons name="layers" size={12} color={theme.secondaryText} />
            <Text style={[styles.statText, { color: theme.secondaryText }]}>
              {note.block_count} blocks
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="text" size={12} color={theme.secondaryText} />
            <Text style={[styles.statText, { color: theme.secondaryText }]}>
              {note.word_count} words
            </Text>
          </View>
        </View>

        {note.blocks && note.blocks.length > 0 && (
          <View style={styles.blockTypes}>
            {note.blocks.slice(0, 4).map((block, index) => (
              <Ionicons
                key={index}
                name={getBlockTypeIcon(block.type) as any}
                size={16}
                color={theme.secondaryText}
                style={styles.blockTypeIcon}
              />
            ))}
            {note.blocks.length > 4 && (
              <Text style={[styles.moreBlocks, { color: theme.secondaryText }]}>
                +{note.blocks.length - 4}
              </Text>
            )}
          </View>
        )}

        {note.tags && note.tags.length > 0 && (
          <View style={styles.tags}>
            {note.tags.slice(0, 2).map((tag, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: theme.background }]}>
                <Text style={[styles.tagText, { color: theme.secondaryText }]}>
                  {tag}
                </Text>
              </View>
            ))}
            {note.tags.length > 2 && (
              <Text style={[styles.moreTags, { color: theme.secondaryText }]}>
                +{note.tags.length - 2}
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};


// =======================
// Main Home Screen Component
// =======================
const HomeScreen = () => {
  const { user } = useUser();
  const router = useRouter();
  const { theme } = useTheme();
  const { userProfile, isProfileLoading } = useAuth();
  const notesService = useNotesService();

  // State
  const [recentNotes, setRecentNotes] = useState<Note[]>([]);
  const [pinnedNotes, setPinnedNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // =======================
  // Data Loading
  // =======================
  const loadNotes = async () => {
    try {
      // Load recent notes (last 10)
      const allNotes = await notesService.getUserNotes();
      const recent = allNotes.slice(0, 10);
      const pinned = allNotes.filter(note => note.is_pinned);

      setRecentNotes(recent);
      setPinnedNotes(pinned);
    } catch (error) {
      console.error('Error loading notes:', error);
      Alert.alert('Error', 'Failed to load notes');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    // Only load notes when user profile is available
    if (userProfile && !isProfileLoading) {
      loadNotes();
    }
  }, [userProfile, isProfileLoading]);

  // =======================
  // Handlers
  // =======================
  const handleRefresh = () => {
    setIsRefreshing(true);
    loadNotes();
  };

  const handleNotePress = (note: Note) => {
    router.push(`/note-editor?noteId=${note.id}`);
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'text':
        router.push('/note-editor?startWith=TEXT');
        break;
      case 'audio':
        router.push('/note-editor?startWith=AUDIO');
        break;
      
    }
  };

  const navigateToAllNotes = () => {
    router.push('/(app)/(authenticated)/(tabs)/allnotes');
  };

  if (isLoading || isProfileLoading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading your notes...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <ScrollView 
        contentContainerStyle={styles.container} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.headerRow}>
          <View style={styles.headerLeft}>
            <Image
              source={{ uri: user?.imageUrl }}
              style={styles.avatar}
            />
            <View>
              <Text style={[styles.greeting, { color: theme.text }]}>
                Hi, <Text style={styles.greetingName}>{user?.firstName || 'User'}</Text>
              </Text>
              <Text style={[styles.subtitle, { color: theme.secondaryText }]}>
                Welcome back to your notes
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.menuButton}>
            <Ionicons name="notifications-outline" size={24} color={theme.text} />
          </TouchableOpacity>
        </View>

        {/* User Profile Status */}
        <UserProfileStatus />

     <Link href="/(app)/(authenticated)/onboard" >onboard</Link>
        
          
        

        {/* Pinned Notes */}
        {pinnedNotes.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: theme.text }]}>Pinned Notes</Text>
              <Ionicons name="pin" size={16} color="#F59E0B" />
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.horizontalNotesList}>
                {pinnedNotes.map((note) => (
                  <NoteCard
                    key={note.id}
                    note={note}
                    onPress={() => handleNotePress(note)}
                    theme={theme}
                  />
                ))}
              </View>
            </ScrollView>
          </View>
        )}

        {/* Recent Notes */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>Recent Notes</Text>
            <TouchableOpacity onPress={navigateToAllNotes}>
              <Text style={[styles.seeAllText, { color: '#3B82F6' }]}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {recentNotes.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="document-outline" size={48} color={theme.secondaryText} />
              <Text style={[styles.emptyStateText, { color: theme.text }]}>No notes yet</Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.secondaryText }]}>
                Create your first note using the quick actions above
              </Text>
            </View>
          ) : (
            <View style={styles.recentNotesList}>
              {recentNotes.map((note) => (
                <NoteCard
                  key={note.id}
                  note={note}
                  onPress={() => handleNotePress(note)}
                  theme={theme}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    padding: 20,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  greeting: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  greetingName: {
    color: '#3B82F6',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  menuButton: {
    padding: 8,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    padding:12,
    paddingBottom:16
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    gap: 8,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  quickActionSubtitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  horizontalNotesList: {
    flexDirection: 'row',
    gap: 12,
  },
  recentNotesList: {
    gap: 12,
  },
  noteCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    minWidth: 280,
  },
  noteCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  noteCardTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 6,
  },
  pinIcon: {
    marginRight: 4,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  noteDate: {
    fontSize: 12,
  },
  noteCardContent: {
    gap: 8,
  },
  noteStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
  },
  blockTypes: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  blockTypeIcon: {
    marginRight: 4,
  },
  moreBlocks: {
    fontSize: 12,
  },
  tags: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  tagText: {
    fontSize: 11,
  },
  moreTags: {
    fontSize: 11,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    gap: 12,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
