import { StyleSheet, Platform } from 'react-native';
import { TABS } from './config';

export type ThemeType = {
  background: string; text: string; secondaryText: string; card: string; border: string;
  primaryButton: string; secondaryButton: string; success: string; warning: string;
  error: string; info: string; highlight: string; primary?: string; [key: string]: any;
};

export const getStyles = (theme: ThemeType, screenWidth: number) => {
  const totalSlots = TABS.length;
  const navBarMaxWidth = 500;
  const horizontalMargin = screenWidth > navBarMaxWidth ? (screenWidth - navBarMaxWidth) / 2 : 24;
  const containerWidth = screenWidth - horizontalMargin * 2;
  const tabWidth = containerWidth / totalSlots;
  const indicatorWidth = tabWidth - 16;

  const styles = StyleSheet.create({
    tabBarWrapper: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      height: 105,
      alignItems: 'center',
    },
    tabBarContainer: {
      flexDirection: 'row',
      height: 74,
      width: containerWidth,
      alignItems: 'center',
      borderRadius: 32,
      backgroundColor: theme.card,
      overflow: 'visible', // Critical: Allows the button to be drawn outside the container
      ...Platform.select({
        ios: { shadowColor: '#000', shadowOffset: { width: 0, height: 5 }, shadowOpacity: 0.1, shadowRadius: 12 },
        android: { elevation: 10 },
      }),
    },
    activeTabIndicator: {
      position: 'absolute',
      left: (tabWidth - indicatorWidth) / 2,
      top: 8,
      width: indicatorWidth,
      height: 58,
      borderRadius: 24,
      backgroundColor: '#FF6F3C',
    },
    centerButtonContainer: {
        width: tabWidth,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    centerButton: {
      position: 'absolute',
      top: -22, // Pushes the button up to make it "float"
      width: 80,
      height: 90,
      borderRadius: 30,
      backgroundColor: "white",
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 4,
      borderColor: theme.card,
      shadowColor: theme.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    centerButtonIcon: {
      color: theme.text,
    },
    tabItemContainer: {
      width: tabWidth,
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      zIndex: 1, // Keep icons above the indicator
    },
    tabItem: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    contentContainer: {
      flex: 1,
      backgroundColor: theme.background,
    },
  });

  return { styles, tabWidth };
};
