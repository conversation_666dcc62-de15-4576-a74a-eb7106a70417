import React, { useCallback, useRef } from 'react';
import { Tabs } from 'expo-router';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import CustomTabBar from './components/CustomTabBar';
import { TABS } from './config';
import BottomSheet, { BottomSheetRef } from './components/BottomSheet';

export default function TabLayout() {
  const bottomSheetModalRef = useRef<BottomSheetRef>(null);

  const handlePresentModalPress = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  return (
    <BottomSheetModalProvider>
      <Tabs
        tabBar={(props) => <CustomTabBar {...props} onCenterButtonPress={handlePresentModalPress} />}
        screenOptions={{ headerShown: false }}
      >
        {TABS.filter((t) => !t.isCenter).map((tab) => (
          <Tabs.Screen key={tab.name} name={tab.name} />
        ))}
        <Tabs.Screen key="center" name="center" options={{ href: null }} />
      </Tabs>

      <BottomSheet ref={bottomSheetModalRef} />
    </BottomSheetModalProvider>
  );
}
