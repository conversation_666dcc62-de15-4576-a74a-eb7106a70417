import React, { useMemo } from 'react';
import { Tabs, usePathname } from 'expo-router';
import { StyleSheet, View, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

// --- TYPE DEFINITIONS ---
type IoniconName = keyof typeof Ionicons.glyphMap;
// --- CHANGE: Simplified config, only one icon needed ---
type TabConfig = {
  name: string;
  icon: IoniconName;
};

// --- CONFIGURATION ---
// --- CHANGE: Using a single icon name for each tab ---
const TABS: TabConfig[] = [
  
   { name: 'index', icon: 'home' },
   
   { name: 'premium', icon: 'star' },
   
  
   {name:"test" , icon:"airplane"},
   {name: "profile", icon:"person"}
];

const COLORS = {
  primary: '#000000',
  white: '#FFFFFF',
  inactive: '#ffffff', // The "dark theme" icon color
};

// --- ANIMATABLE COMPONENTS ---
const AnimatedIonicons = Animated.createAnimatedComponent(Ionicons);

// --- The Refined Tab Item Component ---
const TabItem = ({
  tabConfig,
  isFocused,
  onPress,
}: {
  tabConfig: TabConfig;
  isFocused: boolean;
  onPress: () => void;
}) => {
  // Animation for the "squishy" press effect (kept)
  const scaleX = useSharedValue(1);
  const scaleY = useSharedValue(1);

  const handlePressIn = () => {
    scaleX.value = withSpring(1.1);
    scaleY.value = withSpring(0.9);
  };
  const handlePressOut = () => {
    scaleX.value = withSpring(1);
    scaleY.value = withSpring(1);
  };

  // Animation for the background pill (kept)
  const pillAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isFocused ? 1 : 0, { duration: 250 }),
      transform: [{ scale: withSpring(isFocused ? 1 : 0, { damping: 11, stiffness: 100 }) }],
    };
  });

  // --- CHANGE: Simplified container style. No more hop or active scaling. ---
  // Its only job is to handle the press-in squish.
  const iconContainerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scaleX: scaleX.value },
        { scaleY: scaleY.value },
      ],
    };
  });

  // Opacity animations for the cross-fade (kept)
  const inactiveIconStyle = useAnimatedStyle(() => ({
    opacity: withTiming(isFocused ? 0 : 1, { duration: 200 }),
  }));
  const activeIconStyle = useAnimatedStyle(() => ({
    opacity: withTiming(isFocused ? 1 : 0, { duration: 200 }),
  }));

  return (
    <Pressable
      style={styles.tabItemWrapper}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
    >
      <Animated.View style={[styles.activePill, pillAnimatedStyle]} />

      <Animated.View style={[styles.iconContainer, iconContainerAnimatedStyle]}>
        {/* Inactive Icon (Dark) */}
        <AnimatedIonicons
          name={tabConfig.icon}
          size={26}
          color={COLORS.white}
          style={inactiveIconStyle}
        />
        {/* Active Icon (White) */}
        <AnimatedIonicons
          name={tabConfig.icon} // Same icon name
          size={26}
          color={COLORS.primary}
          style={[styles.absoluteIcon, activeIconStyle]}
        />
      </Animated.View>
    </Pressable>
  );
};

// --- The Custom Tab Bar Itself (Unchanged) ---
function CustomTabBar({ state, navigation }: BottomTabBarProps) {
  const pathname = usePathname();
  const dynamicWidth = useMemo(() => {
    const tabCount = TABS.length;
    if (tabCount === 0) return 0;
    const widthPerTab = 75;
    const horizontalPadding = 10;
    return (tabCount * widthPerTab) + horizontalPadding;
  }, []);

  // Hide the tab bar on the lesson screen
  if (pathname.startsWith('/learn')) {
    return null;
  }

  return (
    <View style={styles.tabBarPositioner}>
      <View style={[styles.tabBarContainer, { width: dynamicWidth }]}>
        {state.routes.map((route, index) => {
          const tabConfig = TABS.find(t => t.name === route.name);
          if (!tabConfig) return null;
          const isFocused = state.index === index;
          return (
            <TabItem
              key={route.key}
              tabConfig={tabConfig}
              isFocused={isFocused}
              onPress={() => {
                const event = navigation.emit({
                  type: 'tabPress', target: route.key, canPreventDefault: true,
                });
                if (!isFocused && !event.defaultPrevented) {
                  navigation.navigate(route.name, route.params);
                }
              }}
            />
          );
        })}
      </View>
    </View>
  );
}

// --- Layout Component (Unchanged) ---
export default function TabLayout() {
  return (
    <Tabs
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      {TABS.map(tab => <Tabs.Screen key={tab.name} name={tab.name} />)}
    </Tabs>
  );
}

// --- Styles (Unchanged) ---
const styles = StyleSheet.create({
  tabBarPositioner: {
    position: 'absolute', bottom: 25, left: 0, right: 0, alignItems: 'center',
  },
  tabBarContainer: {
    height: 85,
    borderRadius: 37.5,
    backgroundColor: COLORS.primary,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabItemWrapper: {
    flex: 1, height: '100%', alignItems: 'center', justifyContent: 'center',
  },
  activePill: {
    position: 'absolute', width: 60, height: 60, borderRadius: 30, backgroundColor: COLORS.white,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  absoluteIcon: {
    position: 'absolute',
  },
});