// --------------------
// Imports
// --------------------
import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import Animated, { useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { getStyles } from '../styles';

// --------------------
// Animated TouchableOpacity
// --------------------
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

// --------------------
// Types
// --------------------
type CenterButtonProps = {
  styles: ReturnType<typeof getStyles>['styles'];
  onPress: () => void;
};

// --------------------
// CenterButton Component
// --------------------
const CenterButton: React.FC<CenterButtonProps> = ({ styles, onPress }) => {
  // State for press animation
  const [pressed, setPressed] = useState(false);

  // Animated style for scaling effect
  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [{ scale: withSpring(pressed ? 1.12 : 1) }],
    }),
    [pressed]
  );

  // Render
  return (
    <AnimatedTouchableOpacity
      activeOpacity={0.9}
      onPressIn={() => setPressed(true)}
      onPressOut={() => setPressed(false)}
      onPress={onPress}
      style={[styles.centerButton, animatedStyle]}
    >
      {/* Center "+" Icon */}
      <Ionicons name="add" size={32} color={"black"} />
    </AnimatedTouchableOpacity>
  );
};

// --------------------
// Export
// --------------------
export default CenterButton;
