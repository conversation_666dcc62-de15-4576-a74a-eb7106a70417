// --------------------
// Imports
// --------------------
import React from 'react';
import { TouchableOpacity } from 'react-native';
import Animated, { useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';
import { getStyles } from '../styles';

// --------------------
// Types
// --------------------
type TabItemProps = {
  icon: keyof typeof Ionicons.glyphMap;
  activeIcon: keyof typeof Ionicons.glyphMap;
  isFocused: boolean;
  onPress: () => void;
  styles: ReturnType<typeof getStyles>['styles'];
};

// --------------------
// TabItem Component
// --------------------
const TabItem: React.FC<TabItemProps> = ({
  icon,
  activeIcon,
  isFocused,
  onPress,
  styles,
}) => {
  // Get theme
  const { theme } = useTheme();

  // Determine icon name and color based on focus state
  const iconName = isFocused ? activeIcon : icon;
  const iconColor: string = isFocused ? 'white' : theme.secondaryText;

  // Animated style for scaling and opacity
  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withSpring(isFocused ? 1.05 : 1) }],
    opacity: withSpring(isFocused ? 1 : 0.7),
  }));

  // Render
  return (
    <TouchableOpacity
      style={styles.tabItemContainer}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Animated.View style={styles.tabItem}>
        <Ionicons name={iconName} size={24} color={iconColor} />
      </Animated.View>
    </TouchableOpacity>
  );
};

// --------------------
// Export
// --------------------
export default TabItem;
