// --------------------
// Imports
// --------------------
import React, { useMemo, forwardRef, useCallback } from 'react';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { getStyles } from '../styles';
import { useTheme } from '~/lib/theme';
import { useWindowDimensions } from 'react-native';
import { QuickActionLauncher } from '@/components/quickNote/QuickActionLauncher';

// --------------------
// Types
// --------------------
type BottomSheetProps = {};

export type BottomSheetRef = BottomSheetModal;

// --------------------
// BottomSheet Component
// --------------------
const BottomSheet = forwardRef<BottomSheetRef, BottomSheetProps>((props, ref) => {
  // Snap points for the bottom sheet
  const snapPoints = useMemo(() => ['29%'], []); // Single snap point for quick actions

  // Theme and dimensions
  const { theme } = useTheme();
  const { width } = useWindowDimensions();

  // Styles based on theme and width
  const { styles } = useMemo(() => getStyles(theme, width), [theme, width]);

  // Handle sheet position changes
  const handleSheetChanges = useCallback((index: number) => {
    console.log('Sheet position changed to:', index);
  }, []);

  // Handle closing the sheet
  const handleClose = useCallback(() => {
    if (ref && 'current' in ref && ref.current) {
      ref.current.dismiss();
    }
  }, [ref]);

  // --------------------
  // Render
  // --------------------
  return (
    <BottomSheetModal
      ref={ref}
      onChange={handleSheetChanges}
      snapPoints={snapPoints}
      handleIndicatorStyle={{ backgroundColor: theme.text, margin: 0, padding: 0 }}
      backgroundStyle={{ backgroundColor: theme.background }}
      backdropComponent={(props) => (
        <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />
      )}
      enablePanDownToClose={true}
      enableDismissOnClose={true}
    >
      <BottomSheetView style={styles.contentContainer}>
        {/* Quick Action Launcher inside the sheet */}
        <QuickActionLauncher onClose={handleClose} />
      </BottomSheetView>
    </BottomSheetModal>
  );
});

// --------------------
// Export
// --------------------
export default BottomSheet;
