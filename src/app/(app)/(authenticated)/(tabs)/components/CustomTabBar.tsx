// --------------------
// Imports
// --------------------
import React, { useMemo } from 'react';
import { View, useWindowDimensions } from 'react-native';
import { usePathname } from 'expo-router';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import Animated, { useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { useTheme } from '~/lib/theme';
import { TABS } from '../config';
import { getStyles } from '../styles';
import CenterButton from './CenterButton';
import TabItem from './TabItem';
import { Ionicons } from '@expo/vector-icons';

// --------------------
// Types
// --------------------
type CustomTabBarProps = BottomTabBarProps & {
  onCenterButtonPress: () => void;
};

// --------------------
// CustomTabBar Component
// --------------------
function CustomTabBar({ state, navigation, onCenterButtonPress }: CustomTabBarProps) {
  // Get current pathname
  const pathname = usePathname();

  // Get theme and window width
  const { theme } = useTheme();
  const { width } = useWindowDimensions();

  // Get styles and tab width based on theme and width
  const { styles, tabWidth } = useMemo(() => getStyles(theme, width), [theme, width]);

  // Hide tab bar on /learn routes
  if (pathname.startsWith('/learn')) return null;

  // Get active route and tab index
  const activeRouteName = state.routes[state.index]?.name;
  const activeTabIndex = TABS.findIndex((tab) => tab.name === activeRouteName);

  // Animated style for the active tab indicator
  const animatedIndicatorStyle = useAnimatedStyle(() => {
    const targetX = activeTabIndex > -1 ? activeTabIndex * tabWidth : 0;
    return {
      transform: [{ translateX: withSpring(targetX, { damping: 18, stiffness: 120 }) }],
      opacity: withSpring(activeTabIndex > -1 ? 1 : 0),
    };
  });

  // --------------------
  // Render
  // --------------------
  return (
    <View style={styles.tabBarWrapper} pointerEvents="box-none">
      <View style={styles.tabBarContainer}>
        {/* Animated indicator for the active tab */}
        <Animated.View style={[styles.activeTabIndicator, animatedIndicatorStyle]} />

        {/* Render each tab item */}
        {TABS.map((tab) => {
          // Render the center button if this is the center tab
          if (tab.isCenter) {
            return (
              <View key="center-button-container" style={styles.centerButtonContainer}>
                <CenterButton styles={styles} onPress={onCenterButtonPress} />
              </View>
            );
          }

          // Find the corresponding route for this tab
          const route = state.routes.find((r) => r.name === tab.name);
          if (!route) return null;

          // Determine if this tab is focused
          const isFocused = state.routes[state.index].key === route.key;

          // Render the tab item
          return (
            <TabItem
              key={route.key}
              icon={tab.icon as keyof typeof Ionicons.glyphMap}
              activeIcon={tab.activeIcon as keyof typeof Ionicons.glyphMap}
              isFocused={isFocused}
              onPress={() => {
                const event = navigation.emit({
                  type: 'tabPress',
                  target: route.key,
                  canPreventDefault: true,
                });
                if (!isFocused && !event.defaultPrevented) {
                  navigation.navigate(route.name, route.params);
                }
              }}
              styles={styles}
            />
          );
        })}
      </View>
    </View>
  );
}

// --------------------
// Export
// --------------------
export default CustomTabBar;
