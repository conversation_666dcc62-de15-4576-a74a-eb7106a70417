import { Stack } from 'expo-router';
import React from 'react';
import { AuthGuard } from '@/components/auth/AuthGuard';

const Layout = () => {
  return (
    <AuthGuard requireProfile={true} showLoading={true}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false, presentation: 'modal' }} />
        <Stack.Screen name="onboard" options={{ headerShown: false, presentation: 'modal' }} />
        <Stack.Screen name="note-editor" options={{ headerShown: false, presentation: 'modal' }} />
      </Stack>
    </AuthGuard>
  );
};
export default Layout;
