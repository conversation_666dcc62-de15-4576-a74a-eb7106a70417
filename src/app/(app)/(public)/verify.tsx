import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useRef, useEffect } from 'react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { emailAtom } from '@/store/login';
import { useAtomValue } from 'jotai';
import { isClerkAPIResponseError, useSignUp, useSignIn } from '@clerk/clerk-expo';

const VerifyEmailScreen = () => {
  const router = useRouter();
  const { isLogin } = useLocalSearchParams();
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [countdown, setCountdown] = useState(60);
  const [isTimerRunning, setIsTimerRunning] = useState(true);
  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null, null, null]);
  const email = useAtomValue(emailAtom);
  const { isLoaded: signUpLoaded, signUp, setActive: setActiveSignUp } = useSignUp();
  const { isLoaded: signInLoaded, signIn, setActive: setActiveSignIn } = useSignIn();

  const isSignInFlow = isLogin === 'true';

  useEffect(() => {
    // Focus first input on mount
    inputRefs.current[0]?.focus();
  }, []);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isTimerRunning && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      setIsTimerRunning(false);
    }
    return () => clearInterval(timer);
  }, [countdown, isTimerRunning]);

  const handleCodeChange = (text: string, index: number) => {
    const newCode = [...code];
    newCode[index] = text;
    setCode(newCode);

    // Move to next input if value entered
    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleBackspace = (index: number) => {
    if (!code[index] && index > 0) {
      // If current input is empty and not first input, move to previous
      inputRefs.current[index - 1]?.focus();
    }
  };

  const isCodeComplete = code.every((digit) => digit !== '');

  useEffect(() => {
    if (isCodeComplete) {
      Keyboard.dismiss();
    }
  }, [isCodeComplete]);

  const onPressVerify = async () => {
    if (isSignInFlow) {
      if (!signInLoaded) return;

      try {
        const completeSignIn = await signIn?.attemptFirstFactor({
          strategy: 'email_code',
          code: code.join(''),
        });

        if (completeSignIn?.status === 'complete') {
          await setActiveSignIn!({ session: completeSignIn.createdSessionId });
          router.replace('/(app)/(authenticated)');
        }
      } catch (err: any) {
        Alert.alert('Error', err.errors?.[0]?.longMessage || 'Invalid verification code.');
      }
    } else {
      if (!signUpLoaded) return;

      try {
        const completeSignUp = await signUp?.attemptEmailAddressVerification({
          code: code.join(''),
        });

        if (completeSignUp?.status === 'complete') {
          await setActiveSignUp!({ session: completeSignUp.createdSessionId });
          router.replace('/(app)/(authenticated)');
        }
      } catch (err: any) {
        Alert.alert('Error', err.errors?.[0]?.longMessage || 'Invalid verification code.');
      }
    }
  };

  const handleResendCode = async () => {
    if (countdown === 0) {
      setCountdown(60);
      setIsTimerRunning(true);
    }

    try {
      if (isSignInFlow) {
        // For sign-in, we need to create a new sign-in attempt
        await signIn?.create({
          strategy: 'email_code',
          identifier: email,
        });
      } else {
        await signUp?.prepareEmailAddressVerification({ strategy: 'email_code' });
      }
    } catch (err) {
      console.log('error', JSON.stringify(err, null, 2));
    }
  };

  return (
    <View className="flex-1 justify-center p-5 bg-[#111]">
      <Text className="text-2xl font-bold text-white text-center mb-2.5">Verify Your Email</Text>
      <Text className="text-base text-gray-500 text-center mb-10 px-2.5">
        We've sent a 6-digit code to {email || 'your email address'}. Please enter it below to {isSignInFlow ? 'sign in' : 'continue'}.
      </Text>

      <View className="flex-row justify-between mt-8">
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <TextInput
            key={index}
            ref={(ref) => (inputRefs.current[index] = ref)}
            className={`w-[52px] h-[52px] bg-[#222] rounded-lg text-white text-center text-xl font-bold ${!code[index] && index === code.findIndex((c) => !c) ? 'border-2 border-[#007bff]' : ''}`}
            maxLength={1}
            keyboardType="number-pad"
            value={code[index]}
            caretHidden={true}
            onChangeText={(text) => handleCodeChange(text, index)}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace') {
                const newCode = [...code];
                newCode[index] = '';
                setCode(newCode);
                handleBackspace(index);
              }
            }}
          />
        ))}
      </View>

      <TouchableOpacity className="mt-5 h-[50px] rounded-lg items-center justify-center bg-[#007bff]" onPress={onPressVerify}>
        <Text className="text-white font-bold text-base">
          {isSignInFlow ? 'Verify and Sign In' : 'Verify and Continue'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity className="mt-6 items-center" onPress={handleResendCode}>
        <Text className={`text-base font-medium ${countdown > 0 ? 'text-[#888]' : 'text-[#007bff]'}`}>
          Resend code {countdown > 0 ? `(${countdown})` : ''}
        </Text>
      </TouchableOpacity>
    </View>
  );
};



export default VerifyEmailScreen;
