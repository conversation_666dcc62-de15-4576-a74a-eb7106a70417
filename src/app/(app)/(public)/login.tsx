import React, { useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  Alert,
  Pressable,
} from 'react-native';
import { useAuth, useSSO } from '@clerk/clerk-expo';
import { Ionicons } from '@expo/vector-icons';
import { Redirect } from 'expo-router';

const Login = () => {
  const { startSSOFlow } = useSSO();
  const [loading, setLoading] = useState<'google' | false>(false);
  const { isSignedIn, isLoaded } = useAuth();

  if (!isLoaded) {
    return <View />;
  }

  if (isSignedIn) {
    return <Redirect href="/(app)/(authenticated)/(tabs)" />;
  }

  const handleSignInWithSSO = async () => {
    setLoading('google');
    try {
      const { createdSessionId, setActive } = await startSSOFlow({
        strategy: 'oauth_google',
      });

      if (createdSessionId) {
        setActive!({ session: createdSessionId });
      }
    } catch (err) {
      console.error('OAuth error', err);
      Alert.alert('OAuth Error', 'Something went wrong, please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="flex-1 justify-center p-5 bg-[#111]">
      <Text className="text-3xl text-white text-center mb-2.5">
        Welcome!
      </Text>
      <Text className="text-base text-gray-500 text-center mb-10">
        Sign in with Google
      </Text>

      <Pressable
        className="bg-[#4285F4] mt-2.5 h-[50px] rounded-lg items-center justify-center flex-row"
        onPress={handleSignInWithSSO}
        disabled={!!loading}
      >
        {loading === 'google' ? (
          <ActivityIndicator color="white" />
        ) : (
          <>
            <Ionicons name="logo-google" size={24} color="white" />
            <Text className="text-white text-base ml-3">
              Continue with Google
            </Text>
          </>
        )}
      </Pressable>
    </View>
  );
};

export default Login;
