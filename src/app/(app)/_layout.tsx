import { Slot } from 'expo-router';
import { View, Text, StyleSheet } from 'react-native';
import { useAppStatus } from '~/lib/AppStatusProvider';

const Layout = () => {
  const { isOnline } = useAppStatus();

  return (
    <View style={{ flex: 1 }}>
      <Slot />
      {!isOnline && (
        <View style={styles.offlineBanner}>
          <Text style={styles.offlineText}>You are offline</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  offlineBanner: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#D9534F',
    padding: 12,
    alignItems: 'center',
  },
  offlineText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default Layout;
