import { Redirect } from 'expo-router';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@clerk/clerk-expo';

const Page = () => {
  const { isSignedIn, isLoaded } = useAuth();

  if (!isLoaded) {
    // Optionally, you can show a loading indicator here
    return null;
  }

  if (isSignedIn) {
    return (
      <SafeAreaView className='flex-1 bg-white'>
        <Redirect href="/(app)/(authenticated)/(tabs)/profile" />
      </SafeAreaView>
    );
  } else {
    return (
      <SafeAreaView className='flex-1 bg-white'>
        <Redirect href="/(app)/(public)/login" />
      </SafeAreaView>
    );
  }
};

export default Page;
