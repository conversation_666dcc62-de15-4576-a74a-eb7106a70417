import { Redirect } from 'expo-router';
import { useAuth } from '@clerk/clerk-expo';
import { useOnboardingState } from '~/lib/onboardingState';
import { ActivityIndicator, View } from 'react-native';

const Page = () => {
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const { isOnboardingComplete, isLoaded: isOnboardingLoaded } = useOnboardingState();

  if (!isAuthLoaded || !isOnboardingLoaded) {
    // Show a loading indicator while checking auth and onboarding status
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!isOnboardingComplete) {
    return <Redirect href="/(app)/(authenticated)/onboard" />;
  }

  if (!isSignedIn) {
    return <Redirect href="/(app)/(public)/login" />;
  }

  return <Redirect href="/(app)/(authenticated)/(tabs)" />;
};

export default Page;
