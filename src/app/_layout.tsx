import './global.css';
import { Slot, SplashScreen } from 'expo-router';
import { LogBox, useColorScheme, View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { tokenCache } from '@/utils/cache';
import { ClerkProvider, ClerkLoaded, useAuth } from '@clerk/clerk-expo';

import { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider, useTheme } from 'lib/theme';

import { OnboardingStateProvider } from 'lib/onboardingState';
import React from 'react';

import { GlobalErrorBoundary } from '@/components/ErrorBoundary';


SplashScreen.preventAutoHideAsync();


const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  );
}
LogBox.ignoreLogs(['Clerk: Clerk has been loaded with development keys.']);


// AppContent component with theme-aware status bar
const AppContent: React.FC = () => {
  const { theme, isDarkMode } = useTheme();
  useEffect(() => {
    SplashScreen.hideAsync();
  }, []);

  return (
    <>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} backgroundColor={theme.background} />
      <GlobalErrorBoundary
        onError={(error, errorInfo) => {
          // TODO: Report to crash analytics service
          console.error('🚨 Global app error:', error, errorInfo);
        }}
      >
        <ClerkProvider
          publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}
          tokenCache={tokenCache}
        >
          <BottomSheetModalProvider>
            <Slot />
          </BottomSheetModalProvider>
        </ClerkProvider>
      </GlobalErrorBoundary>
    </>
  );
};

export default function RootLayout() {
  const colorScheme = useColorScheme();
  

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <OnboardingStateProvider>
        <ThemeProvider>
          <AppContent />
        </ThemeProvider>
      </OnboardingStateProvider>
    </GestureHandlerRootView>
  );
}
