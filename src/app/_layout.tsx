import "./global.css";
import { Slot, SplashScreen } from "expo-router";
import { LogBox, useColorScheme, View, Text } from "react-native";
import { tokenCache } from "@/utils/cache";
import { ClerkProvider, ClerkLoaded, useAuth } from "@clerk/clerk-expo";
import { ConvexReactClient } from "convex/react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { useEffect } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { ThemeProvider } from "lib/theme";
import {
  useQuery,
  useMutation,
  useQueryClient,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";

import { OnboardingStateProvider } from "lib/onboardingState";
import { AppStatusProvider } from "lib/AppStatusProvider";

SplashScreen.preventAutoHideAsync();

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    "Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env"
  );
}
LogBox.ignoreLogs(["Clerk: Clerk has been loaded with development keys."]);

const convex = new ConvexReactClient(process.env.EXPO_PUBLIC_CONVEX_URL!, {
  unsavedChangesWarning: false,
});

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const queryClient = new QueryClient();

  useEffect(() => {
    SplashScreen.hideAsync();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <OnboardingStateProvider>
        <AppStatusProvider>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <ThemeProvider>
              <ClerkProvider
                publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}
                tokenCache={tokenCache}
              >
                <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
                  <Slot />
                </ConvexProviderWithClerk>
              </ClerkProvider>
            </ThemeProvider>
          </GestureHandlerRootView>
        </AppStatusProvider>
      </OnboardingStateProvider>{" "}
    </QueryClientProvider>
  );
}
