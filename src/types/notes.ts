// Core note and block types for the Audio Notes App
export type BlockType = 'AUDIO';

// Content type for audio blocks
export interface AudioBlockContent {
  url: string;
  duration_ms: number;
  transcription?: string;
  title?: string;
}

// Union type for block content (simplified to audio only)
export type BlockContent = AudioBlockContent;

// Core block interface
export interface ContentBlock {
  id: string;
  note_id: string;
  user_id: string;
  type: BlockType;
  sort_order: number;
  content: BlockContent;
  embedding?: number[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Note interface
export interface Note {
  id: string;
  user_id: string;
  notebook_id: string;
  title: string;
  summary?: string;
  tags: string[];
  is_favorite: boolean;
  is_archived: boolean;
  is_pinned: boolean;
  view_count: number;
  word_count: number;
  block_count: number;
  last_viewed_at?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  blocks?: ContentBlock[];
  notebook?: {
    id: string;
    name: string;
    color: string;
    icon: string;
  };
}

// Organizational types
export interface Notebook {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  is_default: boolean;
  sort_order: number;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_at: string;
}

export interface Tag {
  id: string;
  user_id: string;
  name: string;
}

export interface NoteTag {
  note_id: string;
  tag_id: string;
}

// Draft types for note creation
export interface NoteDraft {
  title?: string;
  notebook_id?: string;
  tags: string[];
  blocks: Omit<ContentBlock, 'id' | 'note_id' | 'user_id' | 'created_at' | 'updated_at'>[];
}

// Quick creation modes (simplified to audio only)
export type QuickCreationMode = 'audio';

// UI state types
export interface QuickNoteState {
  mode: QuickCreationMode;
  draft: NoteDraft;
  isRecording: boolean;
  isUploading: boolean;
  error?: string;
}
