// =======================
// Imports
// =======================
import AsyncStorage from '@react-native-async-storage/async-storage';

// =======================
// Types & Interfaces
// =======================
export interface CachedNote {
  id: string;
  title: string;
  content?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  notebook_id: string;
  is_pinned: boolean;
  is_favorite: boolean;
  cached_at: string;
}

export interface CachedUserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  cached_at: string;
}

export interface OfflineData {
  notes: CachedNote[];
  userProfile: CachedUserProfile | null;
  lastSync: string;
}

// =======================
// Storage Keys
// =======================
const STORAGE_KEYS = {
  OFFLINE_NOTES: '@offline_notes',
  OFFLINE_USER_PROFILE: '@offline_user_profile',
  LAST_SYNC: '@last_sync',
  PENDING_UPLOADS: '@pending_uploads',
} as const;

// =======================
// Cache Management
// =======================
export class OfflineStorageManager {
  // =======================
  // Notes Caching
  // =======================
  static async cacheNotes(notes: any[]): Promise<void> {
    try {
      const cachedNotes: CachedNote[] = notes.map(note => ({
        id: note.id,
        title: note.title || 'Untitled Note',
        content: note.content,
        created_at: note.created_at,
        updated_at: note.updated_at,
        user_id: note.user_id,
        notebook_id: note.notebook_id,
        is_pinned: note.is_pinned || false,
        is_favorite: note.is_favorite || false,
        cached_at: new Date().toISOString(),
      }));

      await AsyncStorage.setItem(
        STORAGE_KEYS.OFFLINE_NOTES,
        JSON.stringify(cachedNotes)
      );

      await AsyncStorage.setItem(
        STORAGE_KEYS.LAST_SYNC,
        new Date().toISOString()
      );

      console.log(`[OfflineStorage] Cached ${cachedNotes.length} notes`);
    } catch (error) {
      console.error('[OfflineStorage] Error caching notes:', error);
    }
  }

  static async getCachedNotes(): Promise<CachedNote[]> {
    try {
      const cachedData = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_NOTES);
      if (!cachedData) return [];

      const notes: CachedNote[] = JSON.parse(cachedData);
      console.log(`[OfflineStorage] Retrieved ${notes.length} cached notes`);
      return notes;
    } catch (error) {
      console.error('[OfflineStorage] Error retrieving cached notes:', error);
      return [];
    }
  }

  // =======================
  // User Profile Caching
  // =======================
  static async cacheUserProfile(profile: any): Promise<void> {
    try {
      const cachedProfile: CachedUserProfile = {
        id: profile.id,
        email: profile.email,
        full_name: profile.full_name,
        avatar_url: profile.avatar_url,
        cached_at: new Date().toISOString(),
      };

      await AsyncStorage.setItem(
        STORAGE_KEYS.OFFLINE_USER_PROFILE,
        JSON.stringify(cachedProfile)
      );

      console.log('[OfflineStorage] Cached user profile');
    } catch (error) {
      console.error('[OfflineStorage] Error caching user profile:', error);
    }
  }

  static async getCachedUserProfile(): Promise<CachedUserProfile | null> {
    try {
      const cachedData = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_USER_PROFILE);
      if (!cachedData) return null;

      const profile: CachedUserProfile = JSON.parse(cachedData);
      console.log('[OfflineStorage] Retrieved cached user profile');
      return profile;
    } catch (error) {
      console.error('[OfflineStorage] Error retrieving cached user profile:', error);
      return null;
    }
  }

  // =======================
  // Pending Uploads Management
  // =======================
  static async addPendingUpload(uploadData: any): Promise<void> {
    try {
      const existingUploads = await this.getPendingUploads();
      const updatedUploads = [...existingUploads, {
        ...uploadData,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
      }];

      await AsyncStorage.setItem(
        STORAGE_KEYS.PENDING_UPLOADS,
        JSON.stringify(updatedUploads)
      );

      console.log('[OfflineStorage] Added pending upload');
    } catch (error) {
      console.error('[OfflineStorage] Error adding pending upload:', error);
    }
  }

  static async getPendingUploads(): Promise<any[]> {
    try {
      const cachedData = await AsyncStorage.getItem(STORAGE_KEYS.PENDING_UPLOADS);
      if (!cachedData) return [];

      const uploads = JSON.parse(cachedData);
      console.log(`[OfflineStorage] Retrieved ${uploads.length} pending uploads`);
      return uploads;
    } catch (error) {
      console.error('[OfflineStorage] Error retrieving pending uploads:', error);
      return [];
    }
  }

  static async removePendingUpload(uploadId: string): Promise<void> {
    try {
      const existingUploads = await this.getPendingUploads();
      const filteredUploads = existingUploads.filter(upload => upload.id !== uploadId);

      await AsyncStorage.setItem(
        STORAGE_KEYS.PENDING_UPLOADS,
        JSON.stringify(filteredUploads)
      );

      console.log('[OfflineStorage] Removed pending upload');
    } catch (error) {
      console.error('[OfflineStorage] Error removing pending upload:', error);
    }
  }

  // =======================
  // Sync Status
  // =======================
  static async getLastSyncTime(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
    } catch (error) {
      console.error('[OfflineStorage] Error getting last sync time:', error);
      return null;
    }
  }

  static async updateLastSyncTime(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.LAST_SYNC,
        new Date().toISOString()
      );
    } catch (error) {
      console.error('[OfflineStorage] Error updating last sync time:', error);
    }
  }

  // =======================
  // Cache Validation
  // =======================
  static async isCacheStale(maxAgeHours: number = 24): Promise<boolean> {
    try {
      const lastSync = await this.getLastSyncTime();
      if (!lastSync) return true;

      const lastSyncTime = new Date(lastSync).getTime();
      const now = new Date().getTime();
      const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert hours to milliseconds

      return (now - lastSyncTime) > maxAge;
    } catch (error) {
      console.error('[OfflineStorage] Error checking cache staleness:', error);
      return true;
    }
  }

  // =======================
  // Clear Cache
  // =======================
  static async clearCache(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.OFFLINE_NOTES),
        AsyncStorage.removeItem(STORAGE_KEYS.OFFLINE_USER_PROFILE),
        AsyncStorage.removeItem(STORAGE_KEYS.LAST_SYNC),
      ]);

      console.log('[OfflineStorage] Cache cleared');
    } catch (error) {
      console.error('[OfflineStorage] Error clearing cache:', error);
    }
  }

  // =======================
  // Get All Offline Data
  // =======================
  static async getAllOfflineData(): Promise<OfflineData> {
    try {
      const [notes, userProfile, lastSync] = await Promise.all([
        this.getCachedNotes(),
        this.getCachedUserProfile(),
        this.getLastSyncTime(),
      ]);

      return {
        notes,
        userProfile,
        lastSync: lastSync || '',
      };
    } catch (error) {
      console.error('[OfflineStorage] Error getting all offline data:', error);
      return {
        notes: [],
        userProfile: null,
        lastSync: '',
      };
    }
  }
}
