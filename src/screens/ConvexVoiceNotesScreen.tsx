// =======================
// Convex Voice Notes Screen
// =======================
import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { ConvexVoiceRecorder } from '@/components/voice/ConvexVoiceRecorder';
import { 
  useUserNotes, 
  useSearchNotes,
  useConvexNotesService,
  NoteFilters 
} from '@/services/convexNotesService';
import { 
  useUserNotebooks, 
  useDefaultNotebook,
  useConvexNotebooksService 
} from '@/services/convexNotebooksService';
import { Id } from '../../convex/_generated/dataModel';

// =======================
// Types
// =======================
interface FilterState {
  search: string;
  selectedNotebook?: Id<'notebooks'>;
  showFavorites: boolean;
  showArchived: boolean;
}

// =======================
// Component
// =======================
export const ConvexVoiceNotesScreen: React.FC = () => {
  // State
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    selectedNotebook: undefined,
    showFavorites: false,
    showArchived: false,
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showRecorder, setShowRecorder] = useState(false);

  // Services
  const { toggleFavorite, toggleArchive, deleteNote } = useConvexNotesService();
  const { createNotebook } = useConvexNotebooksService();

  // Data
  const notebooks = useUserNotebooks();
  const defaultNotebook = useDefaultNotebook();
  
  // Build note filters
  const noteFilters: NoteFilters = {
    ...(filters.selectedNotebook && { notebookId: filters.selectedNotebook }),
    ...(filters.showFavorites && { isFavorite: true }),
    ...(filters.showArchived && { isArchived: true }),
    limit: 50,
  };

  // Get notes based on search or filters
  const searchNotes = useSearchNotes(filters.search, 50);
  const filteredNotes = useUserNotes(filters.search ? {} : noteFilters);
  const notes = filters.search.trim() ? searchNotes : filteredNotes;

  // =======================
  // Handlers
  // =======================
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Convex automatically refreshes data, so we just need to wait a moment
    setTimeout(() => setIsRefreshing(false), 1000);
  }, []);

  const handleRecordingComplete = useCallback((audioFileId: Id<'audioFiles'>, noteId: Id<'notes'>) => {
    console.log('[ConvexVoiceNotesScreen] Recording completed:', { audioFileId, noteId });
    setShowRecorder(false);
    Alert.alert(
      'Recording Complete',
      'Your voice note has been saved and is being transcribed.',
      [{ text: 'OK' }]
    );
  }, []);

  const handleRecordingError = useCallback((error: string) => {
    console.error('[ConvexVoiceNotesScreen] Recording error:', error);
    Alert.alert('Recording Error', error);
  }, []);

  const handleToggleFavorite = useCallback(async (noteId: Id<'notes'>, currentValue: boolean) => {
    try {
      await toggleFavorite(noteId, currentValue);
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorite status');
    }
  }, [toggleFavorite]);

  const handleToggleArchive = useCallback(async (noteId: Id<'notes'>, currentValue: boolean) => {
    try {
      await toggleArchive(noteId, currentValue);
    } catch (error) {
      Alert.alert('Error', 'Failed to update archive status');
    }
  }, [toggleArchive]);

  const handleDeleteNote = useCallback(async (noteId: Id<'notes'>, noteTitle: string) => {
    Alert.alert(
      'Delete Note',
      `Are you sure you want to delete "${noteTitle}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteNote(noteId);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete note');
            }
          },
        },
      ]
    );
  }, [deleteNote]);

  // =======================
  // Render Functions
  // =======================
  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.title}>Voice Notes</Text>
      <TouchableOpacity
        style={styles.recordButton}
        onPress={() => setShowRecorder(!showRecorder)}
      >
        <Ionicons 
          name={showRecorder ? 'close' : 'add'} 
          size={24} 
          color="white" 
        />
      </TouchableOpacity>
    </View>
  );

  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search notes..."
          value={filters.search}
          onChangeText={(text) => setFilters(prev => ({ ...prev, search: text }))}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Filter Buttons */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterButtons}>
        <TouchableOpacity
          style={[styles.filterButton, filters.showFavorites && styles.filterButtonActive]}
          onPress={() => setFilters(prev => ({ 
            ...prev, 
            showFavorites: !prev.showFavorites,
            showArchived: false 
          }))}
        >
          <Ionicons name="heart" size={16} color={filters.showFavorites ? 'white' : '#6B7280'} />
          <Text style={[styles.filterButtonText, filters.showFavorites && styles.filterButtonTextActive]}>
            Favorites
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, filters.showArchived && styles.filterButtonActive]}
          onPress={() => setFilters(prev => ({ 
            ...prev, 
            showArchived: !prev.showArchived,
            showFavorites: false 
          }))}
        >
          <Ionicons name="archive" size={16} color={filters.showArchived ? 'white' : '#6B7280'} />
          <Text style={[styles.filterButtonText, filters.showArchived && styles.filterButtonTextActive]}>
            Archived
          </Text>
        </TouchableOpacity>

        {/* Notebook Filters */}
        {notebooks?.map((notebook) => (
          <TouchableOpacity
            key={notebook._id}
            style={[
              styles.filterButton,
              filters.selectedNotebook === notebook._id && styles.filterButtonActive
            ]}
            onPress={() => setFilters(prev => ({ 
              ...prev, 
              selectedNotebook: prev.selectedNotebook === notebook._id ? undefined : notebook._id 
            }))}
          >
            <Text style={[
              styles.filterButtonText,
              filters.selectedNotebook === notebook._id && styles.filterButtonTextActive
            ]}>
              {notebook.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderVoiceRecorder = () => {
    if (!showRecorder) return null;

    return (
      <View style={styles.recorderContainer}>
        <ConvexVoiceRecorder
          onRecordingComplete={handleRecordingComplete}
          onError={handleRecordingError}
          notebookId={filters.selectedNotebook || defaultNotebook?._id}
        />
      </View>
    );
  };

  const renderNoteItem = (note: any) => (
    <View key={note._id} style={styles.noteItem}>
      <View style={styles.noteHeader}>
        <Text style={styles.noteTitle} numberOfLines={1}>
          {note.title}
        </Text>
        <View style={styles.noteActions}>
          <TouchableOpacity
            onPress={() => handleToggleFavorite(note._id, note.isFavorite)}
          >
            <Ionicons
              name={note.isFavorite ? 'heart' : 'heart-outline'}
              size={20}
              color={note.isFavorite ? '#EF4444' : '#6B7280'}
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleToggleArchive(note._id, note.isArchived)}
            style={styles.actionButton}
          >
            <Ionicons
              name={note.isArchived ? 'unarchive' : 'archive'}
              size={20}
              color="#6B7280"
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleDeleteNote(note._id, note.title)}
            style={styles.actionButton}
          >
            <Ionicons name="trash" size={20} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.noteMetadata}>
        <Text style={styles.noteDate}>
          {new Date(note.updatedAt).toLocaleDateString()}
        </Text>
        <Text style={styles.noteStats}>
          {note.blockCount} blocks • {note.wordCount} words
        </Text>
      </View>

      {note.tags && note.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {note.tags.slice(0, 3).map((tag: string, index: number) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>#{tag}</Text>
            </View>
          ))}
          {note.tags.length > 3 && (
            <Text style={styles.moreTagsText}>+{note.tags.length - 3} more</Text>
          )}
        </View>
      )}

      {note.isPinned && (
        <View style={styles.pinnedIndicator}>
          <Ionicons name="pin" size={16} color="#F59E0B" />
        </View>
      )}
    </View>
  );

  const renderNotesList = () => {
    if (!notes) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading notes...</Text>
        </View>
      );
    }

    if (notes.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="mic-outline" size={64} color="#D1D5DB" />
          <Text style={styles.emptyTitle}>No voice notes yet</Text>
          <Text style={styles.emptySubtitle}>
            Tap the + button to create your first voice note
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.notesList}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
      >
        {notes.map(renderNoteItem)}
      </ScrollView>
    );
  };

  // =======================
  // Main Render
  // =======================
  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      {renderFilters()}
      {renderVoiceRecorder()}
      {renderNotesList()}
    </SafeAreaView>
  );
};

// =======================
// Styles
// =======================
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  recordButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  filtersContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  filterButtons: {
    paddingHorizontal: 20,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
  },
  filterButtonActive: {
    backgroundColor: '#3B82F6',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  filterButtonTextActive: {
    color: 'white',
  },
  recorderContainer: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  notesList: {
    flex: 1,
  },
  noteItem: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 8,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginRight: 12,
  },
  noteActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    marginLeft: 12,
  },
  noteMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  noteStats: {
    fontSize: 12,
    color: '#6B7280',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#1E40AF',
  },
  moreTagsText: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  pinnedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
