# =====================================================
# PRODUCTION SUPABASE CONFIGURATION
# =====================================================
# Replace with your actual Supabase project credentials
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_KEY=your-actual-anon-key-here

# =====================================================
# CLERK AUTHENTICATION
# =====================================================
# Get from Clerk dashboard
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your-clerk-key-here

# =====================================================
# DEEPGRAM API (for transcription)
# =====================================================
# Get from Deepgram console
EXPO_PUBLIC_DEEPGRAM_API_KEY=your-deepgram-api-key-here

# =====================================================
# ENVIRONMENT SETTINGS
# =====================================================
NODE_ENV=production
