
# Project Vision: The Universal Notes App

This document outlines the vision, architecture, and data model for our intelligent, block-based notes application. It serves as a comprehensive guide for development.

---

## 1. Core Concept: The "Universal Note"

The fundamental idea is to move beyond simple text notes. A "note" in our app is a **container** or a canvas where the user can add various **blocks** of content. This modular, block-based approach provides immense flexibility, allowing a single note to hold text, images, checklists, videos, audio recordings, and more.

---

## 2. Key Features

### 2.1. Block-Based Content Creation

The following content blocks will be supported:

- **Text Block:** The foundation. Supports rich text formatting via Markdown.
- **Checklist Block:** An interactive list of items with checkboxes.
- **Image Block:** Upload images from the gallery or camera. Supports simple gallery layouts.
- **Audio Block:** Record audio directly in the app. Will feature **automatic transcription** using the integrated Deepgram SDK, making audio content searchable.
- **YouTube Block:** Paste a YouTube link to embed the video player directly in the note.
- **Location Block:** Pin a location on a map, which is displayed as a small map preview.
- **Web Bookmark Block:** Paste any URL to create a rich bookmark with a title, description, and preview image.
- **Sketch Block:** A simple canvas for handwritten notes or drawings.
- **File Block:** For attaching other file types like PDFs or documents.

### 2.2. Organization & Management

- **Notebooks:** A traditional folder-like structure for grouping notes.
- **Tags:** A flexible, non-hierarchical way to categorize notes. A note can have multiple tags.
- **Universal Search:** A powerful search that can find text within:
  - Text blocks
  - Audio transcriptions
  - Text inside images (OCR - future goal)
  - Titles of web bookmarks

### 2.3. AI-Powered Assistant (RAG)

The "AI Chat" feature will be a personal knowledge assistant powered by **Retrieval-Augmented Generation (RAG)**. Its knowledge base will be the user's own notes.

- **How it Works:**
  1.  **Indexing:** When a note is saved, its text content is converted into a numerical representation (embedding) and stored in a vector database (Supabase `pgvector`).
  2.  **Retrieval:** When a user asks a question, the system searches the vector database for the most relevant blocks of content from their notes.
  3.  **Generation:** The user's question and the retrieved content are sent to an LLM to generate a context-aware, accurate answer.
- **Example Use Cases:**
  - "Summarize my notes on 'Project X'."
  - "What was the address I saved for the new office?"
  - "Draft a blog post based on my 'Initial Brainstorm' note."

---

## 3. Detailed Data Model (Supabase)

This is the architectural foundation of the app.

### Table: `notes`
*Stores metadata for each note.*

| Column | Type | Constraints & Notes |
| :--- | :--- | :--- |
| `id` | `uuid` | **Primary Key**. `default gen_random_uuid()` |
| `user_id` | `uuid` | **Foreign Key** -> `auth.users.id`. |
| `title` | `text` | `NULLABLE`. |
| `notebook_id` | `uuid` | **Foreign Key** -> `notebooks.id`. `NULLABLE`. |
| `is_pinned` | `boolean` | `default false`. |
| `is_archived` | `boolean` | `default false`. |
| `created_at` | `timestamptz` | `default now()`. |
| `updated_at` | `timestamptz` | `default now()`. (Auto-updated via trigger). |
| `deleted_at` | `timestamptz` | `NULLABLE`. For soft-deletes. |

### Table: `content_blocks`
*The core table. Each row is a single block of content.*

| Column | Type | Constraints & Notes |
| :--- | :--- | :--- |
| `id` | `uuid` | **Primary Key**. |
| `note_id` | `uuid` | **Foreign Key** -> `notes.id`. `ON DELETE CASCADE`. |
| `user_id` | `uuid` | **Foreign Key** -> `auth.users.id`. (Denormalized for RLS). |
| `type` | `enum` | Custom Enum: `('TEXT', 'CHECKLIST', 'IMAGE', 'AUDIO', ...)` |
| `order` | `integer` | The vertical position of the block (0, 1, 2...). |
| `content` | `jsonb` | The data for the block (see examples below). |
| `embedding` | `vector(1536)` | **For AI/RAG**. Stores OpenAI embeddings. `NULLABLE`. |
| `created_at` | `timestamptz` | `default now()`. |
| `updated_at` | `timestamptz` | `default now()`. |

**Example `content` JSONB:**
- **`TEXT`**: `{ "markdown": "# Heading" }`
- **`CHECKLIST`**: `{ "items": [ { "id": "uuid1", "text": "Buy milk", "checked": false } ] }`
- **`IMAGE`**: `{ "url": "storage/path/image.jpg", "caption": "A caption." }`
- **`AUDIO`**: `{ "url": "storage/path/audio.mp3", "duration_ms": 125000, "transcription": "..." }`

### Organizational Tables

- **`notebooks`**: `id`, `user_id`, `name`, `cover_image_url`, `created_at`
- **`tags`**: `id`, `user_id`, `name` (with `UNIQUE(user_id, name)`)
- **`note_tags`** (Join Table): `note_id`, `tag_id`

### User-Specific Table

- **`user_profiles`**: `id` (1-to-1 with `auth.users`), `display_name`, `avatar_url`, `onboarding_completed`, `settings` (jsonb)

---

## 4. Profile Screen & User Dashboard

The profile screen will be a valuable hub, not just a settings page.

- **Tier 1: Essentials**
  - Avatar and name editing.
  - Theme selector (`ThemeToggle` component).
  - Link to Clerk's hosted account management page.
  - Logout and Delete Account buttons.

- **Tier 2: Value-Adds (Dashboard)**
  - **Statistics Snapshot:** Visual breakdown of notes, blocks, and content types.
  - **Storage Usage:** A progress bar showing used/available storage, with a button to upgrade (future monetization).
  - **Activity Feed:** A list of the last 5 edited notes for quick access.

- **Tier 3: "Wow" Features (Future Vision)**
  - **AI-Powered Insights:** A word cloud of your most used topics, suggested tags for notes.
  - **Data Portability:** An "Export All Data" button.
  - **Deep Customization:** Allow users to customize the note toolbar and set default note templates.

---

## 5. Theming & Visual Identity

The app has a robust, pre-built theming system that supports:
- Light, Dark, and System modes.
- Persistence via `AsyncStorage`.
- A full color palette defined in `constants/colors.ts`.
- Integration with Tailwind CSS via `tailwind.config.js` and CSS variables.
- A `useTheme` hook for easy access in components.

This foundation will be used to ensure all new components are theme-aware and contribute to a polished, consistent UI.
