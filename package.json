{"name": "expo-convex-clerk-template", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@babel/runtime": "^7.27.6", "@clerk/backend": "^2.1.0", "@clerk/clerk-expo": "^2.9.6", "@codeherence/react-native-header": "^0.14.0", "@expo-google-fonts/andika": "^0.4.0", "@expo-google-fonts/cambay": "^0.4.0", "@expo-google-fonts/chivo": "^0.4.1", "@expo-google-fonts/heebo": "^0.4.1", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.5.0", "@react-navigation/native": "^7.0.14", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@rn-primitives/accordion": "^1.2.0", "@rn-primitives/avatar": "^1.2.0", "@rn-primitives/hover-card": "^1.2.0", "@rn-primitives/portal": "^1.3.0", "@rn-primitives/slot": "^1.2.0", "@sentry/react-native": "^6.14.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "1.12.4", "@tanstack/react-query": "^5.83.0", "@types/three": "^0.177.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.22.0", "expo": "~53.0.13", "expo-asset": "~11.1.5", "expo-av": "^15.1.7", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.6", "expo-checkbox": "^4.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.3", "expo-font": "^13.3.1", "expo-gl": "~15.1.6", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-media-library": "^17.1.7", "expo-modules-autolinking": "^2.1.14", "expo-modules-core": "^2.4.2", "expo-navigation-bar": "~4.2.7", "expo-network": "~7.1.5", "expo-permissions": "^14.4.0", "expo-router": "~5.1.1", "expo-secure-store": "^14.0.1", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.9", "expo-three": "^8.0.0", "expo-web-browser": "~14.2.0", "jotai": "^2.12.5", "lodash": "^4.17.21", "lottie-react-native": "7.2.2", "lucide-react": "^0.522.0", "lucide-react-native": "^0.522.0", "moti": "^0.30.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "react-native": "0.79.4", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-marked": "^7.0.2", "react-native-reanimated": "3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "rive-react-native": "^9.3.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.14", "jest": "^29.2.1", "jest-expo": "~53.0.7", "typescript": "^5.3.3"}, "private": true}