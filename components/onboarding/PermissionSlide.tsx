import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface PermissionSlideProps {
  question: string;
  description?: string;
  allowLabel?: string;
  denyLabel?: string;
  illustration?: string;
  onAllow: () => void;
  onDeny: () => void;
  color: string;
}

export function PermissionSlide({ question, description, allowLabel = 'Allow', denyLabel = 'Not now', illustration, onAllow, onDeny, color }: PermissionSlideProps) {
  return (
    <View style={{ width: '100%', alignItems: 'center' }}>
      {illustration && <Text style={styles.illustration}>{illustration}</Text>}
      <Text style={styles.question}>{question}</Text>
      {description && <Text style={styles.description}>{description}</Text>}
      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.btn, { backgroundColor: color }]} onPress={onAllow}>
          <Text style={styles.btnText}>{allowLabel}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.btn, { backgroundColor: '#eee' }]} onPress={onDeny}>
          <Text style={[styles.btnText, { color: '#222' }]}>{denyLabel}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  illustration: {
    fontSize: 64,
    marginBottom: 16,
  },
  question: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
  },
  btn: {
    paddingVertical: 14,
    paddingHorizontal: 28,
    borderRadius: 12,
    marginHorizontal: 8,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 