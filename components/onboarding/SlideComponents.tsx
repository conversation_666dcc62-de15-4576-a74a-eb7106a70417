import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Pressable, TouchableOpacity, TextStyle } from 'react-native';
import { stylesConfig } from './config';
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming, withRepeat, withSequence, Easing } from 'react-native-reanimated';

// --- Slide and Option Types ---

// Type for the "feature" slide (intro, etc)
export type FeatureSlideType = {
  id: string;
  type: 'feature';
  step: string;
  icon: string;
  title: string;
  description: string;
};

// Type for a single option in a choice slide
export type OptionType = {
  id: string;
  text: string;
  icon: string;
};

// Type for a multiple-choice or multiple-select slide
export type ChoiceSlideType = {
  id: string;
  type: 'multiple-choice' | 'multiple-select';
  question: string;
  description?: string;
  options: OptionType[];
};

// Union type for all slide types handled here
export type SlideType = FeatureSlideType | ChoiceSlideType;

// --- Utility: Fix fontWeight for TextStyle (for RN compatibility) ---

function fixFontWeight(style: any): TextStyle {
  if (style && typeof style.fontWeight === 'string') {
    // Only allow valid RN fontWeight values
    const validWeights = [
      'normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900',
      'ultralight', 'thin', 'light', 'medium', 'semibold', 'heavy', 'black', 'regular',
    ];
    if (!validWeights.includes(style.fontWeight)) {
      return { ...style, fontWeight: 'bold' };
    }
  }
  return style;
}

// --- Animated Components ---

// Animated Text for icons, e.g. emoji
const AnimatedText = Animated.createAnimatedComponent(Text);

// Animated icon for the feature slide (bounces up and down)
const AnimatedFeatureIcon = ({ icon }: { icon: string }) => {
  const scale = useSharedValue(1);

  useEffect(() => {
    // Animate scale up and down in a loop
    scale.value = withRepeat(
      withSequence(
        withTiming(1.1, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  return <AnimatedText style={[styles.featureIcon, animatedStyle]}>{icon}</AnimatedText>;
};

// --- Feature Slide (intro, etc) ---

export const FeatureSlide = ({ slide, isVisible }: { slide: FeatureSlideType; isVisible: boolean }) => {
  // Animate opacity and vertical position on appear/disappear
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isVisible ? 1 : 0, { duration: 300 }),
      transform: [{ translateY: withTiming(isVisible ? 0 : -20, { duration: 300 }) }],
    };
  });

  return (
    <Animated.View style={[styles.contentContainer, animatedStyle]} pointerEvents={isVisible ? 'auto' : 'none'}>
      <AnimatedFeatureIcon icon={slide.icon} />
      <Text style={[styles.stepIndicator, fixFontWeight(stylesConfig.stepIndicator)]}>{slide.step}</Text>
      <Text style={[styles.title, fixFontWeight(stylesConfig.title)]}>{slide.title}</Text>
      <Text style={[styles.description, stylesConfig.description, { marginTop: 15 }]}>{slide.description}</Text>
    </Animated.View>
  );
};

// --- Choice Option (single option in a choice/multiselect slide) ---

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const ChoiceOption = ({ option, isSelected, onSelect }: { option: OptionType; isSelected: boolean; onSelect: () => void }) => {
  const scale = useSharedValue(1);
  // Get style config for selected/unselected states
  const unselectedStyle = stylesConfig.optionCard.unselected;
  const selectedStyle = stylesConfig.optionCard.selected;

  // Animate background/border color and scale
  const animatedContainerStyle = useAnimatedStyle(() => {
    const backgroundColor = withTiming(isSelected ? selectedStyle.backgroundColor : unselectedStyle.backgroundColor, { duration: 200 });
    const borderColor = withTiming(isSelected ? selectedStyle.borderColor : unselectedStyle.borderColor, { duration: 200 });
    return {
      transform: [{ scale: scale.value }],
      backgroundColor,
      borderColor,
    };
  });
        
  // Animate text color
  const animatedTextStyle = useAnimatedStyle(() => ({
    color: withTiming(isSelected ? selectedStyle.textColor : unselectedStyle.textColor, { duration: 200 }),
  }));

  // Animate check icon (circle) background and scale
  const animatedCheckIconStyle = useAnimatedStyle(() => ({
    backgroundColor: withTiming(isSelected ? selectedStyle.borderColor : 'transparent', { duration: 200 }),
    transform: [{ scale: isSelected ? withSpring(1, { damping: 15, stiffness: 400 }) : 0.5 }],
  }));

  // Scale down on press in
  const handlePressIn = () => {
    scale.value = withSpring(0.97);
  };

  // Scale up on press out
  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  // Call parent handler on press
  const handlePress = () => {
    onSelect();
  };

  return (
    <AnimatedPressable
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      style={[styles.optionCard, animatedContainerStyle]}
    >
      {/* Option icon (emoji) */}
      <Text style={{ fontSize: stylesConfig.optionCard.iconSize }}>{option.icon}</Text>
      {/* Option text */}
      <Animated.Text style={[styles.optionText, { fontSize: stylesConfig.optionCard.text.fontSize }, animatedTextStyle]}>
        {option.text}
      </Animated.Text>
      {/* Animated check icon (circle) */}
      <Animated.View style={[styles.checkIcon, animatedCheckIconStyle]} />
    </AnimatedPressable>
  );
};

// --- Choice Slide (multiple-choice or multiple-select) ---

export const ChoiceSlide = ({ slide, selectedOptions, handleOptionSelect, isVisible }: {
  slide: ChoiceSlideType;
  selectedOptions: string[];
  handleOptionSelect: (id: string) => void;
  isVisible: boolean;
}) => {
  // Animate opacity and vertical position on appear/disappear
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isVisible ? 1 : 0, { duration: 300 }),
      transform: [{ translateY: withTiming(isVisible ? 0 : -20, { duration: 300 }) }],
    };
  });

  return (
    <Animated.View style={[styles.contentContainer, animatedStyle]} pointerEvents={isVisible ? 'auto' : 'none'}>
      {/* Question */}
      <Text style={[styles.question, fixFontWeight(stylesConfig.question)]}>{slide.question}</Text>
      {/* Optional description */}
      {slide.description && <Text style={[styles.description, stylesConfig.description, { marginTop: 10 }]}>{slide.description}</Text>}
      {/* Options list */}
      <View style={styles.optionsContainer}>
        {slide.options.map((option: OptionType) => (
          <ChoiceOption
            key={option.id}
            option={option}
            isSelected={selectedOptions.includes(option.id)}
            onSelect={() => handleOptionSelect(option.id)}
          />
        ))}
      </View>
    </Animated.View>
  );
};

// --- Styles ---

const styles = StyleSheet.create({
  contentContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 100, // Pushes content up from the button
  },
  featureIcon: {
    fontSize: 100,
    marginBottom: 50,
  },
  stepIndicator: {
    marginBottom: 15,
  },
  title: {
    textAlign: 'center',
    marginBottom: 15,
  },
  question: {
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    textAlign: 'center',
  },
  optionsContainer: {
    width: '100%',
    marginTop: 40,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 24,
    borderWidth: 2,
    marginBottom: 12,
  },
  optionText: {
    marginLeft: 15,
    flex: 1,
    fontWeight: 'bold',
  },
  checkIcon: {
    width: 26,
    height: 26,
    borderRadius: 13,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
});
