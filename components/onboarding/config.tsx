import React from 'react';
import { View, Text } from 'react-native';

// 1. Survey Data - Onboarding Flow for User Purpose & Intent, Learning Style, Motivation, and Personalization
export const surveyData = [
// --- Welcome ---
{
  id: 'welcome',
  type: 'feature',
  step: 'WELCOME',
  icon: '🎤',
  title: 'Capture Ideas Effortlessly',
  description: 'Speak your thoughts, and we’ll turn them into organized notes instantly. Let’s get started!'
},

// --- Permissions ---
{
  id: 'microphone-permission',
  type: 'feature',
  step: 'PERMISSION',
  icon: '🎧',
  title: 'Enable Microphone Access',
  description: 'To take voice notes, we need access to your microphone. Don’t worry, your recordings stay private.'
},

// --- Demo & First Note ---
{
  id: 'demo',
  type: 'feature',
  step: 'DEMO',
  icon: '⚡',
  title: 'Try Your First Voice Note',
  description: 'Tap the mic, say something, and see how we transcribe and summarize it in seconds.'
},

// --- Features & Finish ---
{
  id: 'features',
  type: 'feature',
  step: 'FEATURES',
  icon: '✨',
  title: 'Smart Summaries & Search',
  description: 'We auto-organize notes, generate summaries, and make everything searchable. You’re ready to go!'
}

];

// 2. Styling Configuration - Inspired by Cal AI
export const stylesConfig = {
  container: {
    backgroundColor: '#0D0D0D', // Dark, almost black
  },
  progressSegment: {
    activeColor: '#4CAF50', // A vibrant green
    inactiveColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepIndicator: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    fontWeight: 'bold',
  },
  title: {
    color: '#FFFFFF',
    fontSize: 34,
    fontWeight: 'bold',
    lineHeight: 42,
  },
  question: {
    color: '#FFFFFF',
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
  },
  description: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 18,
    lineHeight: 25,
  },
  button: {
    backgroundColor: '#4CAF50',
    textColor: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold' as 'bold',
  },
  skipButton: {
    textColor: 'rgba(255, 255, 255, 0.6)',
    fontSize: 16,
  },
  backButton: {
    color: 'rgba(255, 255, 255, 0.6)',
  },
  optionCard: {
    iconSize: 28,
    text: {
      fontSize: 18,
    },
    unselected: {
      backgroundColor: '#1A1A1A',
      borderColor: 'transparent',
      textColor: '#FFFFFF',
    },
    selected: {
      backgroundColor: 'rgba(76, 175, 80, 0.15)',
      borderColor: '#4CAF50',
      textColor: '#4CAF50',
    },
  },
};

// 3. Animation Configuration
export const animationConfig = {
  contentTransition: {
    duration: 300,
  },
};

