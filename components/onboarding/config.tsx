import React from 'react';
import { View, Text } from 'react-native';

// 1. Survey Data - Onboarding Flow for User Purpose & Intent, Learning Style, Motivation, and Personalization
export const surveyData = [
// --- Welcome ---
{
  id: 'welcome',
  type: 'feature',
  step: 'WELCOME',
  icon: '🎤',
  title: 'Capture Ideas Effortlessly',
  description: 'Speak your thoughts, and we’ll turn them into organized notes instantly. Let’s get started!'
},

// --- Permissions ---
{
  id: 'microphone-permission',
  type: 'feature',
  step: 'PERMISSION',
  icon: '🎧',
  title: 'Enable Microphone Access',
  description: 'To take voice notes, we need access to your microphone. Don’t worry, your recordings stay private.'
},

// --- Demo & First Note ---
{
  id: 'demo',
  type: 'feature',
  step: 'DEMO',
  icon: '⚡',
  title: 'Try Your First Voice Note',
  description: 'Tap the mic, say something, and see how we transcribe and summarize it in seconds.'
},

// --- Features & Finish ---
{
  id: 'features',
  type: 'feature',
  step: 'FEATURES',
  icon: '✨',
  title: 'Smart Summaries & Search',
  description: 'We auto-organize notes, generate summaries, and make everything searchable. You’re ready to go!'
}

];

