import React from 'react';
import { View, StyleSheet } from 'react-native';

interface ProgressBarProps {
  progress: number; // 0 to 1
  color: string;
  backgroundColor: string;
}

export function ProgressBar({ progress, color, backgroundColor }: ProgressBarProps) {
  return (
    <View style={[styles.bg, { backgroundColor }] }>
      <View style={[styles.fill, { width: `${progress * 100}%`, backgroundColor: color }]} />
    </View>
  );
}

const styles = StyleSheet.create({
  bg: {
    flex: 1,
    height: 8,
    borderRadius: 3,
    overflow: 'hidden',
  },
  fill: {
    height: 8,
    borderRadius: 3,
  },
}); 