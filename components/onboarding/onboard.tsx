import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
} from "react-native-reanimated";
import { useRouter } from "expo-router";
import { surveyData } from "./config";
import { useTheme } from "lib/theme";

import { ProgressBar } from "./ProgressBar";
import { BackButton } from "./BackButton";

import { PermissionSlide } from "./PermissionSlide";
import { useOnboardingState } from "lib/onboardingState";
import { FeatureSlide } from "./FeatureSlide";
import { useAppStatus } from "../../lib/AppStatusProvider";

const { width } = Dimensions.get("window");

// Slide type to component mapping
const SLIDE_COMPONENTS: Record<string, React.ComponentType<any>> = {
  feature: FeatureSlide,
  permission: ({ slide, theme, handleNext }) => {
    const { requestMicrophonePermission } = useAppStatus();
    return (
      <PermissionSlide
        question={slide.question}
        description={slide.description}
        allowLabel={slide.allowLabel}
        denyLabel={slide.denyLabel}
        illustration={slide.illustration}
        color={theme.primaryButton}
        onAllow={async () => {
          await requestMicrophonePermission();
          handleNext();
        }}
        onDeny={handleNext}
      />
    );
  },
};

const Onboard = () => {
  const { isLoaded, lastSlideId, setLastSlideId, completeOnboarding } =
    useOnboardingState();

  const [current, setCurrent] = useState(0);
  const slideAnim = useSharedValue(1);
  const { theme } = useTheme();
  const router = useRouter();

  // When the component loads, determine the starting slide.
  useEffect(() => {
    if (isLoaded && lastSlideId) {
      const lastIndex = surveyData.findIndex((s) => s.id === lastSlideId);
      if (lastIndex > -1) {
        setCurrent(lastIndex);
      }
    }
  }, [isLoaded, lastSlideId]);

  const handleNext = () => {
    const nextSlideIndex = current + 1;
    if (nextSlideIndex < surveyData.length) {
      slideAnim.value = 0;
      setTimeout(() => {
        setCurrent(nextSlideIndex);
        setLastSlideId(surveyData[nextSlideIndex].id);
        slideAnim.value = withTiming(1, { duration: 350 });
      }, 200);
    } else {
      // Reached the end
      completeOnboarding();
      router.push("/(app)/(public)/login");
    }
  };

  const handleBack = () => {
    if (current > 0) {
      slideAnim.value = 0;
      setTimeout(() => {
        const prevSlideIndex = current - 1;
        setCurrent(prevSlideIndex);
        setLastSlideId(surveyData[prevSlideIndex].id);
        slideAnim.value = withTiming(1, { duration: 350 });
      }, 200);
    }
  };

  if (!isLoaded) {
    // You can return a loading spinner here
    return (
      <View style={[styles.container, { backgroundColor: theme.background }]} />
    );
  }

  const slide = surveyData[current];

  const progress = (current + 1) / surveyData.length;

  // Render slide dynamically
  const SlideComponent = SLIDE_COMPONENTS[slide.type];

  const slideAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: slideAnim.value,
      transform: [
        {
          translateX: interpolate(slideAnim.value, [0, 1], [width * 0.1, 0]),
        },
      ],
    };
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Progress Bar & Back Button */}
      <View style={styles.topBar}>
        <BackButton
          onPress={handleBack}
          disabled={current === 0}
          color="#ffffff"
          backgroundColor="transparent"
        />
        <ProgressBar
          progress={progress}
          color={theme.primaryButton}
          backgroundColor={theme.border}
        />
      </View>
      <Animated.View style={[slideAnimatedStyle, { width: "100%", flex: 1 }]}>
        {SlideComponent ? (
          <SlideComponent
            slide={slide}
            theme={theme}
            handleNext={handleNext}
          />
        ) : (
          <View style={styles.centered}>
            <Text style={[styles.title, { color: theme.text }]}>
              Unknown slide type: {slide.type}
            </Text>
          </View>
        )}
      </Animated.View>
      {/* Next Button (hide for permission) */}
      {slide.type !== "permission" && (
        <TouchableOpacity
          onPress={handleNext}
          style={[styles.nextBtn, { backgroundColor: theme.primaryButton }]}
          activeOpacity={0.85}
        >
          <Text style={{ color: "#fff", fontWeight: "bold", fontSize: 18 }}>
            Next
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default Onboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10,
    paddingHorizontal: 24,
    backgroundColor: "#fff",
  },
  topBar: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 32,
  },
  centered: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginTop: 40,
  },
  icon: {
    fontSize: 48,
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  question: {
    fontSize: 26,
    fontWeight: "bold",
    color: "#222",
    marginBottom: 8,
    marginTop: 12,
  },
  subDescription: {
    fontSize: 16,
    color: "#888",
    marginBottom: 16,
  },
  nextBtn: {
    width: "100%",
    paddingVertical: 18,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
    marginTop: 8,
  },
});
