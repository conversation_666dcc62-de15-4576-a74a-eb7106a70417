import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

interface BackButtonProps {
  onPress: () => void;
  disabled?: boolean;
  color: string;
  backgroundColor: string;
}

export function BackButton({ onPress, disabled, color, backgroundColor }: BackButtonProps) {
  return (
    <Pressable
      style={[styles.btn, { backgroundColor, opacity: disabled ? 0 : 1 }]}
      onPress={onPress}
      disabled={disabled}
      accessibilityLabel="Go back"
    >
      <ArrowLeft size={28} color={color} />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  btn: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 2,
  },
}); 