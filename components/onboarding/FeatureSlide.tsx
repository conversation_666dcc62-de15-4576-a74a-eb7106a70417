import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
  interpolate,
  Easing,
} from "react-native-reanimated";
import LottieView from "lottie-react-native";

const AnimatedText = Animated.createAnimatedComponent(Text);
const AnimatedView = Animated.createAnimatedComponent(View);

export const FeatureSlide = ({ slide, theme }: { slide: any; theme: any }) => {
  const iconOpacity = useSharedValue(0);
  const iconScale = useSharedValue(0.5);
  const titleOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(20);
  const descriptionOpacity = useSharedValue(0);
  const descriptionTranslateY = useSharedValue(15);

  useEffect(() => {
    // Animate icon with bounce effect
    iconOpacity.value = withTiming(1, { duration: 600 });
    iconScale.value = withSpring(1, {
      damping: 8,
      stiffness: 100,
      mass: 1,
    });

    // Animate title with slight delay
    titleOpacity.value = withDelay(300, withTiming(1, { duration: 500 }));
    titleTranslateY.value = withDelay(
      300,
      withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.cubic),
      })
    );

    // Animate description with more delay
    descriptionOpacity.value = withDelay(600, withTiming(1, { duration: 500 }));
    descriptionTranslateY.value = withDelay(
      600,
      withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.cubic),
      })
    );
  }, []);

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    opacity: iconOpacity.value,
    transform: [{ scale: iconScale.value }],
  }));

  const titleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: titleTranslateY.value }],
  }));

  const descriptionAnimatedStyle = useAnimatedStyle(() => ({
    opacity: descriptionOpacity.value,
    transform: [{ translateY: descriptionTranslateY.value }],
  }));

  return (
    <View style={styles.centered}>
      <AnimatedView style={iconAnimatedStyle}>
        <Text style={[styles.icon, { color: theme.primaryButton }]}>
          {slide.icon}
        </Text>
      </AnimatedView>

      <AnimatedText
        style={[styles.title, { color: theme.text }, titleAnimatedStyle]}
      >
        {slide.title}
      </AnimatedText>
     
       
        <LottieView
          autoPlay
          style={{
            width: 2000,
            height: 200,
            backgroundColor: "transparent",
          }}
          // Find more Lottie files at https://lottiefiles.com/featured
          source={require("./Voiceline.json")}
        />
      

      <AnimatedText
        style={[
          styles.description,
          { color: theme.secondaryText },
          descriptionAnimatedStyle,
        ]}
      >
        {slide.description}
      </AnimatedText>
    </View>
  );
};

const styles = StyleSheet.create({
  centered: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginTop: 40,
  },
  icon: {
    fontSize: 64,
    marginBottom: 28,
    textShadowColor: "rgba(0, 0, 0, 0.1)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 10,
  },
  title: {
    fontSize: 30,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  description: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 26,
    paddingHorizontal: 20,
  },
});
