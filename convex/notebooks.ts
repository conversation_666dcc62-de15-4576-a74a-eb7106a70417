import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Doc, Id } from './_generated/dataModel';

// =======================
// Notebook Queries
// =======================

/**
 * Get all notebooks for a user
 */
export const getUserNotebooks = query({
  args: {},
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get notebooks ordered by sort order
    const notebooks = await ctx.db
      .query('notebooks')
      .withIndex('byUserIdAndSortOrder', (q) => q.eq('userId', user._id))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .order('asc')
      .collect();

    return notebooks;
  },
});

/**
 * Get a single notebook by ID
 */
export const getNotebook = query({
  args: { notebookId: v.id('notebooks') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get notebook and verify ownership
    const notebook = await ctx.db.get(args.notebookId);
    if (!notebook || notebook.userId !== user._id || notebook.deletedAt) {
      return null;
    }

    return notebook;
  },
});

/**
 * Get default notebook for a user
 */
export const getDefaultNotebook = query({
  args: {},
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get default notebook
    const defaultNotebook = await ctx.db
      .query('notebooks')
      .withIndex('byUserIdAndDefault', (q) => q.eq('userId', user._id).eq('isDefault', true))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .unique();

    return defaultNotebook;
  },
});

// =======================
// Notebook Mutations
// =======================

/**
 * Create a new notebook
 */
export const createNotebook = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
    icon: v.optional(v.string()),
    isDefault: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // If this is set as default, unset other default notebooks
    if (args.isDefault) {
      const existingDefault = await ctx.db
        .query('notebooks')
        .withIndex('byUserIdAndDefault', (q) => q.eq('userId', user._id).eq('isDefault', true))
        .filter((q) => q.eq(q.field('deletedAt'), undefined))
        .unique();

      if (existingDefault) {
        await ctx.db.patch(existingDefault._id, {
          isDefault: false,
          updatedAt: Date.now(),
        });
      }
    }

    // Get next sort order
    const existingNotebooks = await ctx.db
      .query('notebooks')
      .withIndex('byUserId', (q) => q.eq('userId', user._id))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    const maxSortOrder = Math.max(...existingNotebooks.map(nb => nb.sortOrder), -1);

    const now = Date.now();

    // Create notebook
    const notebookId = await ctx.db.insert('notebooks', {
      userId: user._id,
      name: args.name,
      description: args.description,
      color: args.color || '#3B82F6',
      icon: args.icon || 'folder',
      isDefault: args.isDefault || false,
      sortOrder: maxSortOrder + 1,
      metadata: {},
      createdAt: now,
      updatedAt: now,
    });

    return notebookId;
  },
});

/**
 * Update a notebook
 */
export const updateNotebook = mutation({
  args: {
    notebookId: v.id('notebooks'),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
    icon: v.optional(v.string()),
    isDefault: v.optional(v.boolean()),
    sortOrder: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get notebook and verify ownership
    const notebook = await ctx.db.get(args.notebookId);
    if (!notebook || notebook.userId !== user._id || notebook.deletedAt) {
      throw new Error('Notebook not found or access denied');
    }

    // If this is set as default, unset other default notebooks
    if (args.isDefault && !notebook.isDefault) {
      const existingDefault = await ctx.db
        .query('notebooks')
        .withIndex('byUserIdAndDefault', (q) => q.eq('userId', user._id).eq('isDefault', true))
        .filter((q) => q.eq(q.field('deletedAt'), undefined))
        .unique();

      if (existingDefault) {
        await ctx.db.patch(existingDefault._id, {
          isDefault: false,
          updatedAt: Date.now(),
        });
      }
    }

    // Update notebook
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) updateData.name = args.name;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.color !== undefined) updateData.color = args.color;
    if (args.icon !== undefined) updateData.icon = args.icon;
    if (args.isDefault !== undefined) updateData.isDefault = args.isDefault;
    if (args.sortOrder !== undefined) updateData.sortOrder = args.sortOrder;

    await ctx.db.patch(args.notebookId, updateData);

    return args.notebookId;
  },
});

/**
 * Delete a notebook (soft delete)
 */
export const deleteNotebook = mutation({
  args: { notebookId: v.id('notebooks') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get notebook and verify ownership
    const notebook = await ctx.db.get(args.notebookId);
    if (!notebook || notebook.userId !== user._id || notebook.deletedAt) {
      throw new Error('Notebook not found or access denied');
    }

    // Check if this is the default notebook
    if (notebook.isDefault) {
      throw new Error('Cannot delete the default notebook');
    }

    // Check if notebook has notes
    const notesInNotebook = await ctx.db
      .query('notes')
      .withIndex('byNotebookId', (q) => q.eq('notebookId', args.notebookId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    if (notesInNotebook.length > 0) {
      throw new Error('Cannot delete notebook that contains notes. Move or delete notes first.');
    }

    const now = Date.now();

    // Soft delete notebook
    await ctx.db.patch(args.notebookId, {
      deletedAt: now,
      updatedAt: now,
    });

    return args.notebookId;
  },
});

/**
 * Reorder notebooks
 */
export const reorderNotebooks = mutation({
  args: {
    notebookOrders: v.array(v.object({
      notebookId: v.id('notebooks'),
      sortOrder: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    const now = Date.now();

    // Update all notebook orders
    await Promise.all(
      args.notebookOrders.map(async ({ notebookId, sortOrder }) => {
        // Verify notebook ownership
        const notebook = await ctx.db.get(notebookId);
        if (!notebook || notebook.userId !== user._id || notebook.deletedAt) {
          throw new Error(`Notebook ${notebookId} not found or access denied`);
        }

        await ctx.db.patch(notebookId, {
          sortOrder,
          updatedAt: now,
        });
      })
    );

    return true;
  },
});

/**
 * Initialize default notebook for a new user
 */
export const initializeDefaultNotebook = mutation({
  args: {},
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Check if user already has a default notebook
    const existingDefault = await ctx.db
      .query('notebooks')
      .withIndex('byUserIdAndDefault', (q) => q.eq('userId', user._id).eq('isDefault', true))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .unique();

    if (existingDefault) {
      return existingDefault._id;
    }

    const now = Date.now();

    // Create default notebook
    const notebookId = await ctx.db.insert('notebooks', {
      userId: user._id,
      name: 'My Notes',
      description: 'Default notebook for your voice notes',
      color: '#3B82F6',
      icon: 'mic',
      isDefault: true,
      sortOrder: 0,
      metadata: {},
      createdAt: now,
      updatedAt: now,
    });

    return notebookId;
  },
});

/**
 * Get notebook statistics (note count, etc.)
 */
export const getNotebookStats = query({
  args: { notebookId: v.id('notebooks') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Verify notebook ownership
    const notebook = await ctx.db.get(args.notebookId);
    if (!notebook || notebook.userId !== user._id || notebook.deletedAt) {
      throw new Error('Notebook not found or access denied');
    }

    // Get notes in notebook
    const notes = await ctx.db
      .query('notes')
      .withIndex('byNotebookId', (q) => q.eq('notebookId', args.notebookId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    // Calculate statistics
    const stats = {
      noteCount: notes.length,
      totalWordCount: notes.reduce((sum, note) => sum + note.wordCount, 0),
      totalBlockCount: notes.reduce((sum, note) => sum + note.blockCount, 0),
      favoriteCount: notes.filter(note => note.isFavorite).length,
      pinnedCount: notes.filter(note => note.isPinned).length,
      archivedCount: notes.filter(note => note.isArchived).length,
      lastUpdated: Math.max(...notes.map(note => note.updatedAt), notebook.updatedAt),
    };

    return stats;
  },
});
