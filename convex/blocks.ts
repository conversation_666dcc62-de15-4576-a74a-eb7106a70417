import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Doc, Id } from './_generated/dataModel';

// =======================
// Block Queries
// =======================

/**
 * Get all blocks for a note
 */
export const getNoteBlocks = query({
  args: { noteId: v.id('notes') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Verify note ownership
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      throw new Error('Note not found or access denied');
    }

    // Get blocks
    const blocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteIdAndSortOrder', (q) => q.eq('noteId', args.noteId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .order('asc')
      .collect();

    return blocks;
  },
});

// =======================
// Block Mutations
// =======================

/**
 * Create a new block
 */
export const createBlock = mutation({
  args: {
    noteId: v.id('notes'),
    type: v.union(v.literal('AUDIO'), v.literal('TEXT'), v.literal('IMAGE')),
    sortOrder: v.number(),
    content: v.object({
      url: v.optional(v.string()),
      duration_ms: v.optional(v.number()),
      transcription: v.optional(v.string()),
      title: v.optional(v.string()),
      filePath: v.optional(v.string()),
      fileSize: v.optional(v.number()),
      markdown: v.optional(v.string()),
      imageUrl: v.optional(v.string()),
      caption: v.optional(v.string()),
    }),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Verify note ownership
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      throw new Error('Note not found or access denied');
    }

    const now = Date.now();

    // Create block
    const blockId = await ctx.db.insert('blocks', {
      noteId: args.noteId,
      userId: user._id,
      type: args.type,
      sortOrder: args.sortOrder,
      content: args.content,
      metadata: args.metadata || {},
      createdAt: now,
      updatedAt: now,
    });

    // Update note's block count and word count
    const blocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteId', (q) => q.eq('noteId', args.noteId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    const wordCount = blocks.reduce((total, block) => {
      if (block.type === 'AUDIO' && block.content.transcription) {
        return total + block.content.transcription.split(/\s+/).length;
      } else if (block.type === 'TEXT' && block.content.markdown) {
        return total + block.content.markdown.split(/\s+/).length;
      }
      return total;
    }, 0);

    await ctx.db.patch(args.noteId, {
      blockCount: blocks.length,
      wordCount,
      updatedAt: now,
    });

    return blockId;
  },
});

/**
 * Update a block
 */
export const updateBlock = mutation({
  args: {
    blockId: v.id('blocks'),
    content: v.optional(v.object({
      url: v.optional(v.string()),
      duration_ms: v.optional(v.number()),
      transcription: v.optional(v.string()),
      title: v.optional(v.string()),
      filePath: v.optional(v.string()),
      fileSize: v.optional(v.number()),
      markdown: v.optional(v.string()),
      imageUrl: v.optional(v.string()),
      caption: v.optional(v.string()),
    })),
    sortOrder: v.optional(v.number()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get block and verify ownership
    const block = await ctx.db.get(args.blockId);
    if (!block || block.userId !== user._id || block.deletedAt) {
      throw new Error('Block not found or access denied');
    }

    const now = Date.now();

    // Update block
    const updateData: any = {
      updatedAt: now,
    };

    if (args.content) {
      updateData.content = { ...block.content, ...args.content };
    }
    if (args.sortOrder !== undefined) {
      updateData.sortOrder = args.sortOrder;
    }
    if (args.metadata) {
      updateData.metadata = args.metadata;
    }

    await ctx.db.patch(args.blockId, updateData);

    // Update note's word count if content changed
    if (args.content) {
      const allBlocks = await ctx.db
        .query('blocks')
        .withIndex('byNoteId', (q) => q.eq('noteId', block.noteId))
        .filter((q) => q.eq(q.field('deletedAt'), undefined))
        .collect();

      const wordCount = allBlocks.reduce((total, b) => {
        const currentBlock = b._id === args.blockId ? { ...b, content: updateData.content } : b;
        if (currentBlock.type === 'AUDIO' && currentBlock.content.transcription) {
          return total + currentBlock.content.transcription.split(/\s+/).length;
        } else if (currentBlock.type === 'TEXT' && currentBlock.content.markdown) {
          return total + currentBlock.content.markdown.split(/\s+/).length;
        }
        return total;
      }, 0);

      await ctx.db.patch(block.noteId, {
        wordCount,
        updatedAt: now,
      });
    }

    return args.blockId;
  },
});

/**
 * Delete a block (soft delete)
 */
export const deleteBlock = mutation({
  args: { blockId: v.id('blocks') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get block and verify ownership
    const block = await ctx.db.get(args.blockId);
    if (!block || block.userId !== user._id || block.deletedAt) {
      throw new Error('Block not found or access denied');
    }

    const now = Date.now();

    // Soft delete block
    await ctx.db.patch(args.blockId, {
      deletedAt: now,
      updatedAt: now,
    });

    // Update note's block count and word count
    const remainingBlocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteId', (q) => q.eq('noteId', block.noteId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    const wordCount = remainingBlocks.reduce((total, b) => {
      if (b.type === 'AUDIO' && b.content.transcription) {
        return total + b.content.transcription.split(/\s+/).length;
      } else if (b.type === 'TEXT' && b.content.markdown) {
        return total + b.content.markdown.split(/\s+/).length;
      }
      return total;
    }, 0);

    await ctx.db.patch(block.noteId, {
      blockCount: remainingBlocks.length,
      wordCount,
      updatedAt: now,
    });

    return args.blockId;
  },
});

/**
 * Reorder blocks within a note
 */
export const reorderBlocks = mutation({
  args: {
    noteId: v.id('notes'),
    blockOrders: v.array(v.object({
      blockId: v.id('blocks'),
      sortOrder: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Verify note ownership
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      throw new Error('Note not found or access denied');
    }

    const now = Date.now();

    // Update all block orders
    await Promise.all(
      args.blockOrders.map(async ({ blockId, sortOrder }) => {
        // Verify block ownership
        const block = await ctx.db.get(blockId);
        if (!block || block.userId !== user._id || block.noteId !== args.noteId) {
          throw new Error(`Block ${blockId} not found or access denied`);
        }

        await ctx.db.patch(blockId, {
          sortOrder,
          updatedAt: now,
        });
      })
    );

    // Update note's updated timestamp
    await ctx.db.patch(args.noteId, {
      updatedAt: now,
    });

    return args.noteId;
  },
});

/**
 * Update block transcription (used by transcription service)
 */
export const updateBlockTranscription = mutation({
  args: {
    blockId: v.id('blocks'),
    transcription: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get block and verify ownership
    const block = await ctx.db.get(args.blockId);
    if (!block || block.userId !== user._id || block.deletedAt) {
      throw new Error('Block not found or access denied');
    }

    if (block.type !== 'AUDIO') {
      throw new Error('Can only update transcription for audio blocks');
    }

    const now = Date.now();

    // Update block with transcription
    await ctx.db.patch(args.blockId, {
      content: {
        ...block.content,
        transcription: args.transcription,
      },
      updatedAt: now,
    });

    // Update note's word count
    const allBlocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteId', (q) => q.eq('noteId', block.noteId))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .collect();

    const wordCount = allBlocks.reduce((total, b) => {
      const transcription = b._id === args.blockId ? args.transcription : b.content.transcription;
      if (b.type === 'AUDIO' && transcription) {
        return total + transcription.split(/\s+/).length;
      } else if (b.type === 'TEXT' && b.content.markdown) {
        return total + b.content.markdown.split(/\s+/).length;
      }
      return total;
    }, 0);

    await ctx.db.patch(block.noteId, {
      wordCount,
      updatedAt: now,
    });

    return args.blockId;
  },
});
