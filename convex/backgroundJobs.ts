import { cronJobs } from 'convex/server';
import { internalAction, internalMutation, internalQuery } from './_generated/server';
import { internal, api } from './_generated/api';
import { v } from 'convex/values';

// =======================
// Background Job Scheduler
// =======================

/**
 * Process pending transcription jobs every minute
 */
const processTranscriptionJobs = cronJobs.interval(
  'process transcription jobs',
  { minutes: 1 }, // Run every minute
  internal.backgroundJobs.processTranscriptionJobsBatch
);

/**
 * Clean up old completed jobs daily at 2 AM UTC
 */
const cleanupOldJobs = cronJobs.daily(
  'cleanup old jobs',
  { hourUTC: 2, minuteUTC: 0 },
  internal.backgroundJobs.cleanupOldJobsBatch
);

export default { processTranscriptionJobs, cleanupOldJobs };

// =======================
// Background Job Processors
// =======================

/**
 * Process a batch of pending transcription jobs
 */
export const processTranscriptionJobsBatch = internalAction({
  args: {},
  handler: async (ctx, args) => {
    console.log('[BackgroundJobs] Processing transcription jobs batch');

    try {
      // Get pending jobs (limit to 5 at a time to avoid overwhelming the system)
      const pendingJobs = await ctx.runQuery(internal.backgroundJobs.getPendingJobsInternal, { limit: 5 });

      if (pendingJobs.length === 0) {
        console.log('[BackgroundJobs] No pending transcription jobs found');
        return;
      }

      console.log(`[BackgroundJobs] Found ${pendingJobs.length} pending jobs`);

      // Process each job
      const results = await Promise.allSettled(
        pendingJobs.map(async (job) => {
          try {
            console.log(`[BackgroundJobs] Processing job ${job._id}`);
            await ctx.runAction(api.audioFiles.processTranscriptionJob, { jobId: job._id });
            return { jobId: job._id, success: true };
          } catch (error) {
            console.error(`[BackgroundJobs] Error processing job ${job._id}:`, error);
            return { jobId: job._id, success: false, error: error instanceof Error ? error.message : 'Unknown error' };
          }
        })
      );

      // Log results
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      console.log(`[BackgroundJobs] Batch complete: ${successful} successful, ${failed} failed`);

    } catch (error) {
      console.error('[BackgroundJobs] Error in processTranscriptionJobsBatch:', error);
    }
  },
});

/**
 * Clean up old completed/failed jobs
 */
export const cleanupOldJobsBatch = internalAction({
  args: {},
  handler: async (ctx, args) => {
    console.log('[BackgroundJobs] Cleaning up old jobs');

    try {
      // Clean up jobs older than 7 days
      const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000);

      const deletedCount = await ctx.runMutation(internal.backgroundJobs.cleanupOldJobsInternal, {
        cutoffTime,
      });

      console.log(`[BackgroundJobs] Cleaned up ${deletedCount} old jobs`);

    } catch (error) {
      console.error('[BackgroundJobs] Error in cleanupOldJobsBatch:', error);
    }
  },
});

// =======================
// Internal Helper Functions
// =======================

/**
 * Get pending transcription jobs (internal query)
 */
export const getPendingJobsInternal = internalQuery({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Get jobs that are queued and scheduled to run
    let query = ctx.db
      .query('transcriptionJobs')
      .withIndex('byStatus', (q) => q.eq('status', 'queued'))
      .filter((q) => q.lte(q.field('scheduledAt'), now));

    const jobs = await query.order('asc').collect();

    // Apply limit
    if (args.limit) {
      return jobs.slice(0, args.limit);
    }

    return jobs;
  },
});

/**
 * Clean up old jobs (internal mutation)
 */
export const cleanupOldJobsInternal = internalMutation({
  args: { cutoffTime: v.number() },
  handler: async (ctx, args) => {
    // Get old completed/failed jobs
    const oldJobs = await ctx.db
      .query('transcriptionJobs')
      .filter((q) =>
        q.and(
          q.or(
            q.eq(q.field('status'), 'completed'),
            q.eq(q.field('status'), 'failed')
          ),
          q.lt(q.field('updatedAt'), args.cutoffTime)
        )
      )
      .collect();

    // Delete old jobs
    await Promise.all(
      oldJobs.map(async (job) => {
        await ctx.db.delete(job._id);
      })
    );

    return oldJobs.length;
  },
});
