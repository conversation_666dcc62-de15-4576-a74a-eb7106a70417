import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Doc, Id } from './_generated/dataModel';

// =======================
// Transcription Job Queries
// =======================

/**
 * Get transcription job by ID
 */
export const getJob = query({
  args: { jobId: v.id('transcriptionJobs') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get job and verify ownership
    const job = await ctx.db.get(args.jobId);
    if (!job || job.userId !== user._id) {
      return null;
    }

    return job;
  },
});

/**
 * Get pending transcription jobs (for background processing)
 */
export const getPendingJobs = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Get jobs that are queued and scheduled to run
    let query = ctx.db
      .query('transcriptionJobs')
      .withIndex('byStatus', (q) => q.eq('status', 'queued'))
      .filter((q) => q.lte(q.field('scheduledAt'), now));

    const jobs = await query.order('asc').collect();

    // Apply limit
    if (args.limit) {
      return jobs.slice(0, args.limit);
    }

    return jobs;
  },
});

/**
 * Get user's transcription jobs
 */
export const getUserJobs = query({
  args: {
    status: v.optional(v.union(
      v.literal('queued'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Build query
    let query = ctx.db
      .query('transcriptionJobs')
      .withIndex('byUserId', (q) => q.eq('userId', user._id));

    // Apply status filter
    if (args.status) {
      query = query.filter((q) => q.eq(q.field('status'), args.status));
    }

    // Execute query
    let jobs = await query.order('desc').collect();

    // Apply limit
    if (args.limit) {
      jobs = jobs.slice(0, args.limit);
    }

    return jobs;
  },
});

// =======================
// Transcription Job Mutations
// =======================

/**
 * Update transcription job status
 */
export const updateJobStatus = mutation({
  args: {
    jobId: v.id('transcriptionJobs'),
    status: v.union(
      v.literal('queued'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    ),
    attempts: v.optional(v.number()),
    result: v.optional(v.string()),
    error: v.optional(v.string()),
    scheduledAt: v.optional(v.number()),
    startedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get job and verify ownership
    const job = await ctx.db.get(args.jobId);
    if (!job || job.userId !== user._id) {
      throw new Error('Transcription job not found or access denied');
    }

    const updateData: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.attempts !== undefined) {
      updateData.attempts = args.attempts;
    }
    if (args.result !== undefined) {
      updateData.result = args.result;
    }
    if (args.error !== undefined) {
      updateData.error = args.error;
    }
    if (args.scheduledAt !== undefined) {
      updateData.scheduledAt = args.scheduledAt;
    }
    if (args.startedAt !== undefined) {
      updateData.startedAt = args.startedAt;
    }
    if (args.completedAt !== undefined) {
      updateData.completedAt = args.completedAt;
    }

    await ctx.db.patch(args.jobId, updateData);

    return args.jobId;
  },
});

/**
 * Retry a failed transcription job
 */
export const retryJob = mutation({
  args: { jobId: v.id('transcriptionJobs') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get job and verify ownership
    const job = await ctx.db.get(args.jobId);
    if (!job || job.userId !== user._id) {
      throw new Error('Transcription job not found or access denied');
    }

    if (job.status !== 'failed') {
      throw new Error('Can only retry failed jobs');
    }

    if (job.attempts >= job.maxAttempts) {
      throw new Error('Job has exceeded maximum retry attempts');
    }

    const now = Date.now();

    // Reset job to queued status
    await ctx.db.patch(args.jobId, {
      status: 'queued',
      attempts: job.attempts + 1,
      error: undefined,
      scheduledAt: now,
      startedAt: undefined,
      completedAt: undefined,
      updatedAt: now,
    });

    return args.jobId;
  },
});

/**
 * Cancel a transcription job
 */
export const cancelJob = mutation({
  args: { jobId: v.id('transcriptionJobs') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get job and verify ownership
    const job = await ctx.db.get(args.jobId);
    if (!job || job.userId !== user._id) {
      throw new Error('Transcription job not found or access denied');
    }

    if (job.status === 'completed') {
      throw new Error('Cannot cancel completed job');
    }

    if (job.status === 'processing') {
      throw new Error('Cannot cancel job that is currently processing');
    }

    const now = Date.now();

    // Mark job as failed with cancellation message
    await ctx.db.patch(args.jobId, {
      status: 'failed',
      error: 'Job cancelled by user',
      completedAt: now,
      updatedAt: now,
    });

    return args.jobId;
  },
});

/**
 * Clean up old completed/failed jobs (for maintenance)
 */
export const cleanupOldJobs = mutation({
  args: { olderThanDays: v.number() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    const cutoffTime = Date.now() - (args.olderThanDays * 24 * 60 * 60 * 1000);

    // Get old completed/failed jobs
    const oldJobs = await ctx.db
      .query('transcriptionJobs')
      .withIndex('byUserId', (q) => q.eq('userId', user._id))
      .filter((q) => 
        q.and(
          q.or(
            q.eq(q.field('status'), 'completed'),
            q.eq(q.field('status'), 'failed')
          ),
          q.lt(q.field('updatedAt'), cutoffTime)
        )
      )
      .collect();

    // Delete old jobs
    await Promise.all(
      oldJobs.map(async (job) => {
        await ctx.db.delete(job._id);
      })
    );

    return oldJobs.length;
  },
});

/**
 * Get transcription job statistics for a user
 */
export const getJobStats = query({
  args: {},
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get all user jobs
    const allJobs = await ctx.db
      .query('transcriptionJobs')
      .withIndex('byUserId', (q) => q.eq('userId', user._id))
      .collect();

    // Calculate statistics
    const stats = {
      total: allJobs.length,
      queued: allJobs.filter(job => job.status === 'queued').length,
      processing: allJobs.filter(job => job.status === 'processing').length,
      completed: allJobs.filter(job => job.status === 'completed').length,
      failed: allJobs.filter(job => job.status === 'failed').length,
      averageProcessingTime: 0,
      successRate: 0,
    };

    // Calculate average processing time for completed jobs
    const completedJobs = allJobs.filter(job => 
      job.status === 'completed' && job.startedAt && job.completedAt
    );

    if (completedJobs.length > 0) {
      const totalProcessingTime = completedJobs.reduce((sum, job) => 
        sum + (job.completedAt! - job.startedAt!), 0
      );
      stats.averageProcessingTime = totalProcessingTime / completedJobs.length;
    }

    // Calculate success rate
    const finishedJobs = stats.completed + stats.failed;
    if (finishedJobs > 0) {
      stats.successRate = (stats.completed / finishedJobs) * 100;
    }

    return stats;
  },
});
