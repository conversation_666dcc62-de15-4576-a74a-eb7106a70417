import { v } from 'convex/values';
import { mutation, query } from './_generated/server';

// =======================
// Audio File Queries
// =======================

/**
 * Get audio file by ID
 */
export const getAudioFile = query({
  args: { audioFileId: v.id('audioFiles') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      return null;
    }

    return audioFile;
  },
});

/**
 * Get audio files by user with optional filtering
 */
export const getUserAudioFiles = query({
  args: {
    transcriptionStatus: v.optional(v.union(
      v.literal('pending'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Build query
    let query = ctx.db
      .query('audioFiles')
      .withIndex('byUserId', (q) => q.eq('userId', user._id));

    // Apply filters
    if (args.transcriptionStatus) {
      query = query.filter((q) => q.eq(q.field('transcriptionStatus'), args.transcriptionStatus));
    }

    // Execute query
    let audioFiles = await query.order('desc').collect();

    // Apply limit
    if (args.limit) {
      audioFiles = audioFiles.slice(0, args.limit);
    }

    return audioFiles;
  },
});

// =======================
// Audio File Mutations
// =======================

/**
 * Generate upload URL for Convex file storage
 */
export const generateUploadUrl = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Get file URL from storage
 */
export const getFileUrl = query({
  args: { storageId: v.id('_storage') },
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  },
});

/**
 * Create a new audio file record
 */
export const createAudioFile = mutation({
  args: {
    storageId: v.id('_storage'),
    originalFileName: v.string(),
    fileSize: v.number(),
    duration_ms: v.optional(v.number()),
    mimeType: v.string(),
    blockId: v.optional(v.id('blocks')),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    const now = Date.now();

    // Create audio file record
    const audioFileId = await ctx.db.insert('audioFiles', {
      userId: user._id,
      blockId: args.blockId,
      storageId: args.storageId,
      originalFileName: args.originalFileName,
      fileSize: args.fileSize,
      duration_ms: args.duration_ms,
      mimeType: args.mimeType,
      transcriptionStatus: 'pending',
      createdAt: now,
      updatedAt: now,
    });

    return audioFileId;
  },
});

/**
 * Update audio file status
 */
export const updateAudioFileStatus = mutation({
  args: {
    audioFileId: v.id('audioFiles'),
    transcriptionStatus: v.optional(v.union(
      v.literal('pending'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    )),
    transcription: v.optional(v.string()),
    transcriptionError: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      throw new Error('Audio file not found or access denied');
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.transcriptionStatus) {
      updateData.transcriptionStatus = args.transcriptionStatus;
    }
    if (args.transcription) {
      updateData.transcription = args.transcription;
    }
    if (args.transcriptionError) {
      updateData.transcriptionError = args.transcriptionError;
    }

    await ctx.db.patch(args.audioFileId, updateData);

    return args.audioFileId;
  },
});

/**
 * Queue audio file for transcription
 */
export const queueForTranscription = mutation({
  args: { audioFileId: v.id('audioFiles') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      throw new Error('Audio file not found or access denied');
    }

    const now = Date.now();

    // Create transcription job
    const jobId = await ctx.db.insert('transcriptionJobs', {
      audioFileId: args.audioFileId,
      userId: user._id,
      storageId: audioFile.storageId,
      status: 'queued',
      attempts: 0,
      maxAttempts: 3,
      scheduledAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update audio file status
    await ctx.db.patch(args.audioFileId, {
      transcriptionStatus: 'pending',
      updatedAt: now,
    });

    return jobId;
  },
});

// =======================
// Audio File Actions (Simplified)
// =======================

/**
 * Simple transcription trigger (placeholder for background processing)
 */
export const triggerTranscription = mutation({
  args: { audioFileId: v.id('audioFiles') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Update audio file status to processing
    await ctx.db.patch(args.audioFileId, {
      transcriptionStatus: 'processing',
      updatedAt: Date.now(),
    });

    // In a real implementation, this would trigger a background job
    console.log('[Convex] Transcription triggered for audio file:', args.audioFileId);

    return { success: true };
  },
});
