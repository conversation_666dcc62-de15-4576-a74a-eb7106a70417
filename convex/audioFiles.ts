import { v } from 'convex/values';
import { mutation, query, action } from './_generated/server';
import { Doc, Id } from './_generated/dataModel';
import { api } from './_generated/api';

// =======================
// Audio File Queries
// =======================

/**
 * Get audio file by ID
 */
export const getAudioFile = query({
  args: { audioFileId: v.id('audioFiles') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      return null;
    }

    return audioFile;
  },
});

/**
 * Get audio files by user with optional filtering
 */
export const getUserAudioFiles = query({
  args: {
    uploadStatus: v.optional(v.union(
      v.literal('uploading'),
      v.literal('completed'),
      v.literal('failed')
    )),
    transcriptionStatus: v.optional(v.union(
      v.literal('pending'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Build query
    let query = ctx.db
      .query('audioFiles')
      .withIndex('byUserId', (q) => q.eq('userId', user._id));

    // Apply filters
    if (args.uploadStatus) {
      query = query.filter((q) => q.eq(q.field('uploadStatus'), args.uploadStatus));
    }

    if (args.transcriptionStatus) {
      query = query.filter((q) => q.eq(q.field('transcriptionStatus'), args.transcriptionStatus));
    }

    // Execute query
    let audioFiles = await query.order('desc').collect();

    // Apply limit
    if (args.limit) {
      audioFiles = audioFiles.slice(0, args.limit);
    }

    return audioFiles;
  },
});

// =======================
// Audio File Mutations
// =======================

/**
 * Create a new audio file record
 */
export const createAudioFile = mutation({
  args: {
    originalFileName: v.string(),
    filePath: v.string(),
    publicUrl: v.string(),
    fileSize: v.number(),
    duration_ms: v.optional(v.number()),
    mimeType: v.string(),
    blockId: v.optional(v.id('blocks')),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    const now = Date.now();

    // Create audio file record
    const audioFileId = await ctx.db.insert('audioFiles', {
      userId: user._id,
      blockId: args.blockId,
      originalFileName: args.originalFileName,
      filePath: args.filePath,
      publicUrl: args.publicUrl,
      fileSize: args.fileSize,
      duration_ms: args.duration_ms,
      mimeType: args.mimeType,
      uploadStatus: 'completed',
      transcriptionStatus: 'pending',
      createdAt: now,
      updatedAt: now,
    });

    return audioFileId;
  },
});

/**
 * Update audio file status
 */
export const updateAudioFileStatus = mutation({
  args: {
    audioFileId: v.id('audioFiles'),
    uploadStatus: v.optional(v.union(
      v.literal('uploading'),
      v.literal('completed'),
      v.literal('failed')
    )),
    transcriptionStatus: v.optional(v.union(
      v.literal('pending'),
      v.literal('processing'),
      v.literal('completed'),
      v.literal('failed')
    )),
    transcription: v.optional(v.string()),
    transcriptionError: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      throw new Error('Audio file not found or access denied');
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.uploadStatus) {
      updateData.uploadStatus = args.uploadStatus;
    }
    if (args.transcriptionStatus) {
      updateData.transcriptionStatus = args.transcriptionStatus;
    }
    if (args.transcription) {
      updateData.transcription = args.transcription;
    }
    if (args.transcriptionError) {
      updateData.transcriptionError = args.transcriptionError;
    }

    await ctx.db.patch(args.audioFileId, updateData);

    return args.audioFileId;
  },
});

/**
 * Queue audio file for transcription
 */
export const queueForTranscription = mutation({
  args: { audioFileId: v.id('audioFiles') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get audio file and verify ownership
    const audioFile = await ctx.db.get(args.audioFileId);
    if (!audioFile || audioFile.userId !== user._id) {
      throw new Error('Audio file not found or access denied');
    }

    if (audioFile.uploadStatus !== 'completed') {
      throw new Error('Audio file upload not completed');
    }

    const now = Date.now();

    // Create transcription job
    const jobId = await ctx.db.insert('transcriptionJobs', {
      audioFileId: args.audioFileId,
      userId: user._id,
      audioUrl: audioFile.publicUrl,
      status: 'queued',
      attempts: 0,
      maxAttempts: 3,
      scheduledAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update audio file status
    await ctx.db.patch(args.audioFileId, {
      transcriptionStatus: 'pending',
      updatedAt: now,
    });

    return jobId;
  },
});

// =======================
// Audio File Actions
// =======================

/**
 * Process transcription job (to be called by background job processor)
 */
export const processTranscriptionJob = action({
  args: { jobId: v.id('transcriptionJobs') },
  handler: async (ctx, args) => {
    // Get the job
    const job = await ctx.runQuery(api.transcriptionJobs.getJob, { jobId: args.jobId });
    if (!job) {
      throw new Error('Transcription job not found');
    }

    if (job.status !== 'queued') {
      throw new Error('Job is not in queued status');
    }

    // Update job status to processing
    await ctx.runMutation(api.transcriptionJobs.updateJobStatus, {
      jobId: args.jobId,
      status: 'processing',
      startedAt: Date.now(),
    });

    try {
      // Call external transcription service (e.g., Deepgram, OpenAI Whisper)
      const transcriptionResult = await transcribeAudioFile(job.audioUrl);

      if (transcriptionResult.success) {
        // Update job as completed
        await ctx.runMutation(api.transcriptionJobs.updateJobStatus, {
          jobId: args.jobId,
          status: 'completed',
          result: transcriptionResult.transcription,
          completedAt: Date.now(),
        });

        // Update audio file with transcription
        await ctx.runMutation(api.audioFiles.updateAudioFileStatus, {
          audioFileId: job.audioFileId,
          transcriptionStatus: 'completed',
          transcription: transcriptionResult.transcription,
        });

        // If audio file is linked to a block, update the block
        const audioFile = await ctx.runQuery(api.audioFiles.getAudioFile, {
          audioFileId: job.audioFileId,
        });

        if (audioFile?.blockId) {
          await ctx.runMutation(api.blocks.updateBlockTranscription, {
            blockId: audioFile.blockId,
            transcription: transcriptionResult.transcription,
          });
        }
      } else {
        // Handle transcription failure
        const shouldRetry = job.attempts < job.maxAttempts;
        
        if (shouldRetry) {
          // Retry the job
          await ctx.runMutation(api.transcriptionJobs.updateJobStatus, {
            jobId: args.jobId,
            status: 'queued',
            attempts: job.attempts + 1,
            error: transcriptionResult.error,
            scheduledAt: Date.now() + (job.attempts + 1) * 60000, // Exponential backoff
          });
        } else {
          // Mark as failed
          await ctx.runMutation(api.transcriptionJobs.updateJobStatus, {
            jobId: args.jobId,
            status: 'failed',
            error: transcriptionResult.error,
            completedAt: Date.now(),
          });

          await ctx.runMutation(api.audioFiles.updateAudioFileStatus, {
            audioFileId: job.audioFileId,
            transcriptionStatus: 'failed',
            transcriptionError: transcriptionResult.error,
          });
        }
      }
    } catch (error) {
      // Handle unexpected errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      await ctx.runMutation(api.transcriptionJobs.updateJobStatus, {
        jobId: args.jobId,
        status: 'failed',
        error: errorMessage,
        completedAt: Date.now(),
      });

      await ctx.runMutation(api.audioFiles.updateAudioFileStatus, {
        audioFileId: job.audioFileId,
        transcriptionStatus: 'failed',
        transcriptionError: errorMessage,
      });
    }

    return args.jobId;
  },
});

// =======================
// Helper Functions
// =======================

/**
 * External transcription service integration
 * This would be replaced with actual service calls (Deepgram, OpenAI, etc.)
 */
async function transcribeAudioFile(audioUrl: string): Promise<{
  success: boolean;
  transcription?: string;
  error?: string;
}> {
  try {
    // Example implementation for Deepgram
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      return { success: false, error: 'Deepgram API key not configured' };
    }

    const response = await fetch('https://api.deepgram.com/v1/listen', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${deepgramApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: audioUrl,
        model: 'nova-2',
        smart_format: true,
        punctuate: true,
        diarize: false,
      }),
    });

    if (!response.ok) {
      return { success: false, error: `Transcription service error: ${response.status}` };
    }

    const result = await response.json();
    const transcription = result.results?.channels?.[0]?.alternatives?.[0]?.transcript;

    if (!transcription) {
      return { success: false, error: 'No transcription found in response' };
    }

    return { success: true, transcription: transcription.trim() };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown transcription error',
    };
  }
}
