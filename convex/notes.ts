import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Doc, Id } from './_generated/dataModel';

// =======================
// Note Queries
// =======================

/**
 * Get all notes for a user with optional filtering
 */
export const getUserNotes = query({
  args: {
    notebookId: v.optional(v.id('notebooks')),
    tags: v.optional(v.array(v.string())),
    search: v.optional(v.string()),
    isFavorite: v.optional(v.boolean()),
    isArchived: v.optional(v.boolean()),
    isPinned: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Build query
    let notesQuery = ctx.db
      .query('notes')
      .withIndex('byUserIdAndUpdatedAt', (q) => q.eq('userId', user._id))
      .filter((q) => q.eq(q.field('deletedAt'), undefined));

    // Apply filters
    if (args.notebookId) {
      notesQuery = notesQuery.filter((q) => q.eq(q.field('notebookId'), args.notebookId));
    }

    if (args.isFavorite !== undefined) {
      notesQuery = notesQuery.filter((q) => q.eq(q.field('isFavorite'), args.isFavorite));
    }

    if (args.isArchived !== undefined) {
      notesQuery = notesQuery.filter((q) => q.eq(q.field('isArchived'), args.isArchived));
    }

    if (args.isPinned !== undefined) {
      notesQuery = notesQuery.filter((q) => q.eq(q.field('isPinned'), args.isPinned));
    }

    // Execute query
    let notes = await notesQuery.order('desc').collect();

    // Apply tag filtering (client-side for now)
    if (args.tags && args.tags.length > 0) {
      notes = notes.filter(note => 
        args.tags!.some(tag => note.tags.includes(tag))
      );
    }

    // Apply search filtering (client-side for now)
    if (args.search) {
      const searchLower = args.search.toLowerCase();
      notes = notes.filter(note => 
        note.title.toLowerCase().includes(searchLower)
      );
    }

    // Apply limit
    if (args.limit) {
      notes = notes.slice(0, args.limit);
    }

    // Get blocks for each note
    const notesWithBlocks = await Promise.all(
      notes.map(async (note) => {
        const blocks = await ctx.db
          .query('blocks')
          .withIndex('byNoteIdAndSortOrder', (q) => q.eq('noteId', note._id))
          .filter((q) => q.eq(q.field('deletedAt'), undefined))
          .order('asc')
          .collect();

        return {
          ...note,
          blocks,
        };
      })
    );

    return notesWithBlocks;
  },
});

/**
 * Get a single note by ID with all its blocks
 */
export const getNote = query({
  args: { noteId: v.id('notes') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get note
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      return null;
    }

    // Get blocks
    const blocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteIdAndSortOrder', (q) => q.eq('noteId', note._id))
      .filter((q) => q.eq(q.field('deletedAt'), undefined))
      .order('asc')
      .collect();

    return {
      ...note,
      blocks,
    };
  },
});

// =======================
// Note Mutations
// =======================

/**
 * Create a new note
 */
export const createNote = mutation({
  args: {
    title: v.string(),
    notebookId: v.optional(v.id('notebooks')),
    tags: v.optional(v.array(v.string())),
    blocks: v.optional(v.array(v.object({
      type: v.union(v.literal('AUDIO'), v.literal('TEXT'), v.literal('IMAGE')),
      sortOrder: v.number(),
      content: v.object({
        url: v.optional(v.string()),
        duration_ms: v.optional(v.number()),
        transcription: v.optional(v.string()),
        title: v.optional(v.string()),
        filePath: v.optional(v.string()),
        fileSize: v.optional(v.number()),
        markdown: v.optional(v.string()),
        imageUrl: v.optional(v.string()),
        caption: v.optional(v.string()),
      }),
      metadata: v.optional(v.object({})),
    }))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get default notebook if none specified
    let notebookId = args.notebookId;
    if (!notebookId) {
      const defaultNotebook = await ctx.db
        .query('notebooks')
        .withIndex('byUserIdAndDefault', (q) => q.eq('userId', user._id).eq('isDefault', true))
        .unique();
      
      if (!defaultNotebook) {
        throw new Error('No default notebook found');
      }
      notebookId = defaultNotebook._id;
    }

    const now = Date.now();

    // Calculate word count from blocks
    let wordCount = 0;
    if (args.blocks) {
      wordCount = args.blocks.reduce((total, block) => {
        if (block.type === 'AUDIO' && block.content.transcription) {
          return total + block.content.transcription.split(/\s+/).length;
        } else if (block.type === 'TEXT' && block.content.markdown) {
          return total + block.content.markdown.split(/\s+/).length;
        }
        return total;
      }, 0);
    }

    // Create note
    const noteId = await ctx.db.insert('notes', {
      userId: user._id,
      notebookId,
      title: args.title || 'Untitled Note',
      tags: args.tags || [],
      isFavorite: false,
      isArchived: false,
      isPinned: false,
      viewCount: 0,
      wordCount,
      blockCount: args.blocks?.length || 0,
      metadata: {},
      createdAt: now,
      updatedAt: now,
    });

    // Create blocks if provided
    if (args.blocks && args.blocks.length > 0) {
      await Promise.all(
        args.blocks.map(async (block) => {
          await ctx.db.insert('blocks', {
            noteId,
            userId: user._id,
            type: block.type,
            sortOrder: block.sortOrder,
            content: block.content,
            metadata: block.metadata || {},
            createdAt: now,
            updatedAt: now,
          });
        })
      );
    }

    return noteId;
  },
});

/**
 * Update a note
 */
export const updateNote = mutation({
  args: {
    noteId: v.id('notes'),
    title: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    isFavorite: v.optional(v.boolean()),
    isArchived: v.optional(v.boolean()),
    isPinned: v.optional(v.boolean()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get note and verify ownership
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      throw new Error('Note not found or access denied');
    }

    // Update note
    await ctx.db.patch(args.noteId, {
      ...args,
      updatedAt: Date.now(),
    });

    return args.noteId;
  },
});

/**
 * Delete a note (soft delete)
 */
export const deleteNote = mutation({
  args: { noteId: v.id('notes') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get user by external ID (Clerk ID)
    const user = await ctx.db
      .query('users')
      .withIndex('byExternalId', (q) => q.eq('externalId', identity.subject))
      .unique();

    if (!user) {
      throw new Error('User not found');
    }

    // Get note and verify ownership
    const note = await ctx.db.get(args.noteId);
    if (!note || note.userId !== user._id || note.deletedAt) {
      throw new Error('Note not found or access denied');
    }

    const now = Date.now();

    // Soft delete note
    await ctx.db.patch(args.noteId, {
      deletedAt: now,
      updatedAt: now,
    });

    // Soft delete all blocks
    const blocks = await ctx.db
      .query('blocks')
      .withIndex('byNoteId', (q) => q.eq('noteId', args.noteId))
      .collect();

    await Promise.all(
      blocks.map(async (block) => {
        await ctx.db.patch(block._id, {
          deletedAt: now,
          updatedAt: now,
        });
      })
    );

    return args.noteId;
  },
});
