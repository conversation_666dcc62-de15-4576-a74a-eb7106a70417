import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

// =======================
// User Schema
// =======================
export const User = {
  email: v.string(),
  // this the Clerk ID, stored in the subject JWT field
  externalId: v.string(),
  imageUrl: v.optional(v.string()),
  name: v.optional(v.string()),
};

// =======================
// Notebook Schema
// =======================
export const Notebook = {
  userId: v.id('users'),
  name: v.string(),
  description: v.optional(v.string()),
  color: v.string(),
  icon: v.string(),
  isDefault: v.boolean(),
  sortOrder: v.number(),
  metadata: v.optional(v.object({})),
  createdAt: v.number(),
  updatedAt: v.number(),
  deletedAt: v.optional(v.number()),
};

// =======================
// Note Schema
// =======================
export const Note = {
  userId: v.id('users'),
  notebookId: v.id('notebooks'),
  title: v.string(),
  summary: v.optional(v.string()),
  tags: v.array(v.string()),
  isFavorite: v.boolean(),
  isArchived: v.boolean(),
  isPinned: v.boolean(),
  viewCount: v.number(),
  wordCount: v.number(),
  blockCount: v.number(),
  lastViewedAt: v.optional(v.number()),
  metadata: v.optional(v.object({})),
  createdAt: v.number(),
  updatedAt: v.number(),
  deletedAt: v.optional(v.number()),
};

// =======================
// Block Schema (for note content)
// =======================
export const Block = {
  noteId: v.id('notes'),
  userId: v.id('users'),
  type: v.union(v.literal('AUDIO'), v.literal('TEXT'), v.literal('IMAGE')),
  sortOrder: v.number(),
  content: v.object({
    // Audio block content
    url: v.optional(v.string()),
    duration_ms: v.optional(v.number()),
    transcription: v.optional(v.string()),
    title: v.optional(v.string()),
    filePath: v.optional(v.string()),
    fileSize: v.optional(v.number()),
    // Text block content
    markdown: v.optional(v.string()),
    // Image block content
    imageUrl: v.optional(v.string()),
    caption: v.optional(v.string()),
  }),
  embedding: v.optional(v.array(v.number())),
  metadata: v.optional(v.object({})),
  createdAt: v.number(),
  updatedAt: v.number(),
  deletedAt: v.optional(v.number()),
};

// =======================
// Audio File Schema (for tracking audio uploads)
// =======================
export const AudioFile = {
  userId: v.id('users'),
  blockId: v.optional(v.id('blocks')),
  originalFileName: v.string(),
  filePath: v.string(),
  publicUrl: v.string(),
  fileSize: v.number(),
  duration_ms: v.optional(v.number()),
  mimeType: v.string(),
  uploadStatus: v.union(
    v.literal('uploading'),
    v.literal('completed'),
    v.literal('failed')
  ),
  transcriptionStatus: v.union(
    v.literal('pending'),
    v.literal('processing'),
    v.literal('completed'),
    v.literal('failed')
  ),
  transcription: v.optional(v.string()),
  transcriptionError: v.optional(v.string()),
  createdAt: v.number(),
  updatedAt: v.number(),
};

// =======================
// Transcription Queue Schema
// =======================
export const TranscriptionJob = {
  audioFileId: v.id('audioFiles'),
  userId: v.id('users'),
  audioUrl: v.string(),
  status: v.union(
    v.literal('queued'),
    v.literal('processing'),
    v.literal('completed'),
    v.literal('failed')
  ),
  attempts: v.number(),
  maxAttempts: v.number(),
  result: v.optional(v.string()),
  error: v.optional(v.string()),
  scheduledAt: v.number(),
  startedAt: v.optional(v.number()),
  completedAt: v.optional(v.number()),
  createdAt: v.number(),
  updatedAt: v.number(),
};

export default defineSchema({
  users: defineTable(User).index('byExternalId', ['externalId']),

  notebooks: defineTable(Notebook)
    .index('byUserId', ['userId'])
    .index('byUserIdAndDefault', ['userId', 'isDefault'])
    .index('byUserIdAndSortOrder', ['userId', 'sortOrder']),

  notes: defineTable(Note)
    .index('byUserId', ['userId'])
    .index('byNotebookId', ['notebookId'])
    .index('byUserIdAndCreatedAt', ['userId', 'createdAt'])
    .index('byUserIdAndUpdatedAt', ['userId', 'updatedAt'])
    .index('byUserIdAndFavorite', ['userId', 'isFavorite'])
    .index('byUserIdAndPinned', ['userId', 'isPinned'])
    .index('byUserIdAndArchived', ['userId', 'isArchived']),

  blocks: defineTable(Block)
    .index('byNoteId', ['noteId'])
    .index('byUserId', ['userId'])
    .index('byNoteIdAndSortOrder', ['noteId', 'sortOrder'])
    .index('byType', ['type']),

  audioFiles: defineTable(AudioFile)
    .index('byUserId', ['userId'])
    .index('byBlockId', ['blockId'])
    .index('byUploadStatus', ['uploadStatus'])
    .index('byTranscriptionStatus', ['transcriptionStatus']),

  transcriptionJobs: defineTable(TranscriptionJob)
    .index('byStatus', ['status'])
    .index('byAudioFileId', ['audioFileId'])
    .index('byScheduledAt', ['scheduledAt'])
    .index('byUserId', ['userId']),
});
