import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import * as SecureStore from 'expo-secure-store';

const STORAGE_KEY_COMPLETED = 'ONBOARDING_COMPLETED';
const STORAGE_KEY_LAST_SLIDE = 'ONBOARDING_LAST_SLIDE';

interface OnboardingContextValue {
  isLoaded: boolean;
  lastSlideId: string | null;
  setLastSlideId: (slideId: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  isOnboardingComplete: boolean;
  resetOnboarding: () => Promise<void>;
}

const OnboardingStateContext = createContext<OnboardingContextValue | undefined>(undefined);

export function OnboardingStateProvider({ children }: { children: ReactNode }) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [lastSlideId, setLastSlideId] = useState<string | null>(null);
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const [completed, lastSlide] = await Promise.all([
          SecureStore.getItemAsync(STORAGE_KEY_COMPLETED),
          SecureStore.getItemAsync(STORAGE_KEY_LAST_SLIDE),
        ]);
        
        if (completed === 'true') {
          setIsOnboardingComplete(true);
        } else {
          setLastSlideId(lastSlide);
        }
      } catch (e) {
        console.error('Failed to load onboarding progress', e);
      } finally {
        setIsLoaded(true);
      }
    })();
  }, []);

  const setLastSlideIdCallback = useCallback(async (slideId: string) => {
    setLastSlideId(slideId);
    try {
      await SecureStore.setItemAsync(STORAGE_KEY_LAST_SLIDE, slideId);
    } catch (err) {
      console.error('Unable to persist last slide ID', err);
    }
  }, []);

  const completeOnboarding = async () => {
    setLastSlideId(null);
    setIsOnboardingComplete(true);
    try {
      await SecureStore.setItemAsync(STORAGE_KEY_COMPLETED, 'true');
      await SecureStore.deleteItemAsync(STORAGE_KEY_LAST_SLIDE);
    } catch (err) {
      console.error('Unable to persist onboarding completion', err);
    }
  };

  const resetOnboarding = async () => {
    setLastSlideId(null);
    setIsOnboardingComplete(false);
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEY_COMPLETED);
      await SecureStore.deleteItemAsync(STORAGE_KEY_LAST_SLIDE);
    } catch (err) {
      console.error('Unable to reset onboarding', err);
    }
  };

  const value: OnboardingContextValue = {
    isLoaded,
    lastSlideId,
    setLastSlideId: setLastSlideIdCallback,
    completeOnboarding,
    isOnboardingComplete,
    resetOnboarding,
  };
  console.log(value);
  
  return (
    <OnboardingStateContext.Provider value={value}>
      {children}
    </OnboardingStateContext.Provider>
  );
}

export function useOnboardingState() {
  const context = useContext(OnboardingStateContext);
  if (!context) {
    throw new Error('useOnboardingState must be used within an OnboardingStateProvider');
  }
  return context;
}
