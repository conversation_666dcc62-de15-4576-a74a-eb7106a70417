import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { AppState, Linking } from 'react-native';
import * as Permissions from 'expo-permissions';

interface AppStatusContextValue {
  isOnline: boolean;
  microphonePermission: Permissions.PermissionStatus | null;
  requestMicrophonePermission: () => Promise<void>;
}

const AppStatusContext = createContext<AppStatusContextValue | undefined>(undefined);

export function AppStatusProvider({ children }: { children: ReactNode }) {
  const [isOnline, setIsOnline] = useState(true);
  const [microphonePermission, setMicrophonePermission] = useState<Permissions.PermissionStatus | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    const handleAppStateChange = async (nextAppState: any) => {
      if (nextAppState === 'active') {
        const { status } = await Permissions.getAsync(Permissions.AUDIO_RECORDING);
        setMicrophonePermission(status);
      }
    };

    AppState.addEventListener('change', handleAppStateChange);

    // Initial check
    handleAppStateChange('active');

    return () => {
      unsubscribe();
      // AppState.removeEventListener('change', handleAppStateChange);
    };
  }, []);

  const requestMicrophonePermission = async () => {
    const { status } = await Permissions.askAsync(Permissions.AUDIO_RECORDING);
    setMicrophonePermission(status);

    if (status === 'denied') {
      // Guide user to settings if they permanently denied the permission
      Linking.openSettings();
    }
  };

  const value = {
    isOnline,
    microphonePermission,
    requestMicrophonePermission,
  };

  return (
    <AppStatusContext.Provider value={value}>
      {children}
    </AppStatusContext.Provider>
  );
}

export function useAppStatus() {
  const context = useContext(AppStatusContext);
  if (!context) {
    throw new Error('useAppStatus must be used within an AppStatusProvider');
  }
  return context;
}
