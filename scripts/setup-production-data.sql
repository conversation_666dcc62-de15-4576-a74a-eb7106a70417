-- =====================================================
-- PRODUCTION TEST DATA SETUP
-- Run this in your Supabase Dashboard SQL Editor
-- =====================================================

-- Create a test user profile (replace with your actual Clerk user ID)
-- You can get your Clerk user ID from the Clerk dashboard or by logging into your app
INSERT INTO profiles (clerk_user_id, email, first_name, last_name, username) 
VALUES ('your-actual-clerk-user-id', '<EMAIL>', 'Your', 'Name', 'yourusername')
ON CONFLICT (clerk_user_id) DO NOTHING;

-- Create default notebooks for the test user
DO $$
DECLARE
    user_uuid UUID;
BEGIN
    -- Get the user ID (replace the clerk_user_id with your actual one)
    SELECT id INTO user_uuid FROM profiles WHERE clerk_user_id = 'your-actual-clerk-user-id';
    
    IF user_uuid IS NOT NULL THEN
        -- Create default notebooks
        INSERT INTO notebooks (user_id, name, description, color, is_default) VALUES
        (user_uuid, 'My Notes', 'Default notebook for all notes', '#3B82F6', true),
        (user_uuid, 'Work', 'Professional notes and meeting minutes', '#10B981', false),
        (user_uuid, 'Personal', 'Personal thoughts and ideas', '#F59E0B', false),
        (user_uuid, 'Projects', 'Project-related documentation', '#8B5CF6', false),
        (user_uuid, 'Ideas', 'Creative ideas and inspiration', '#EF4444', false)
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'Created notebooks for user: %', user_uuid;
    ELSE
        RAISE NOTICE 'User not found. Please update the clerk_user_id in the script.';
    END IF;
END $$;

-- Create a sample note to test the system
DO $$
DECLARE
    user_uuid UUID;
    notebook_uuid UUID;
    note_uuid UUID;
BEGIN
    -- Get user and default notebook
    SELECT id INTO user_uuid FROM profiles WHERE clerk_user_id = 'your-actual-clerk-user-id';
    SELECT id INTO notebook_uuid FROM notebooks WHERE user_id = user_uuid AND is_default = true;
    
    IF user_uuid IS NOT NULL AND notebook_uuid IS NOT NULL THEN
        -- Create a welcome note
        INSERT INTO notes (user_id, notebook_id, title, tags, is_pinned)
        VALUES (user_uuid, notebook_uuid, 'Welcome to Universal Notes!', ARRAY['welcome', 'getting-started'], true)
        RETURNING id INTO note_uuid;
        
        -- Add a text block
        INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
        (note_uuid, user_uuid, 'TEXT', 0, jsonb_build_object(
            'markdown', '# Welcome to Universal Notes! 🎉

Congratulations! Your Universal Notes app is now connected to your production Supabase database.

## What you can do:
- ✅ Create multi-block notes with text, audio, images, and more
- ✅ Record audio with automatic transcription
- ✅ Organize notes in custom notebooks
- ✅ Tag and search your content
- ✅ Sync across all your devices

## Next steps:
1. Try recording an audio note using the "Record Audio" quick action
2. Create different types of content blocks
3. Organize your notes into notebooks
4. Explore the search and tagging features

Happy note-taking! 📝'
        ));
        
        -- Add a checklist block
        INSERT INTO blocks (note_id, user_id, type, sort_order, content) VALUES
        (note_uuid, user_uuid, 'CHECKLIST', 1, jsonb_build_object(
            'items', jsonb_build_array(
                jsonb_build_object('text', 'Set up production Supabase database', 'completed', true),
                jsonb_build_object('text', 'Configure environment variables', 'completed', true),
                jsonb_build_object('text', 'Test audio recording functionality', 'completed', false),
                jsonb_build_object('text', 'Create your first custom notebook', 'completed', false),
                jsonb_build_object('text', 'Try the different block types', 'completed', false)
            )
        ));
        
        RAISE NOTICE 'Created welcome note for user: %', user_uuid;
    ELSE
        RAISE NOTICE 'User or notebook not found. Please check the clerk_user_id.';
    END IF;
END $$;
