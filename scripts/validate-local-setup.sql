-- =====================================================
-- LOCAL SETUP VALIDATION QUERIES
-- Run these in your Supabase Dashboard SQL Editor
-- =====================================================

-- Test 1: Verify all tables exist
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'notebooks', 'notes', 'blocks')
ORDER BY tablename;

-- Test 2: Check foreign key relationships
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
AND tc.table_name IN ('notebooks', 'notes', 'blocks');

-- Test 3: Verify sample data integrity
SELECT 
    p.email,
    COUNT(DISTINCT n.id) as notebook_count,
    COUNT(DISTINCT nt.id) as note_count,
    COUNT(DISTINCT b.id) as block_count
FROM profiles p
LEFT JOIN notebooks n ON p.id = n.user_id
LEFT JOIN notes nt ON p.id = nt.user_id  
LEFT JOIN blocks b ON p.id = b.user_id
GROUP BY p.id, p.email
ORDER BY p.email;

-- Test 4: Check storage buckets
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets
ORDER BY name;

-- Test 5: Verify RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Test 6: Check block types and content
SELECT 
    type,
    COUNT(*) as count,
    jsonb_object_keys(content) as content_keys
FROM blocks 
GROUP BY type, jsonb_object_keys(content)
ORDER BY type;

-- Test 7: Verify triggers exist
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'public'
AND event_object_table IN ('profiles', 'notebooks', 'notes', 'blocks')
ORDER BY event_object_table, trigger_name;

-- Test 8: Check note statistics are updating
SELECT 
    n.title,
    n.block_count,
    n.word_count,
    COUNT(b.id) as actual_block_count
FROM notes n
LEFT JOIN blocks b ON n.id = b.note_id AND b.deleted_at IS NULL
GROUP BY n.id, n.title, n.block_count, n.word_count
HAVING n.block_count != COUNT(b.id);

-- Test 9: Sample audio block content
SELECT 
    n.title,
    b.type,
    b.content->>'url' as audio_url,
    b.content->>'duration_ms' as duration,
    length(b.content->>'transcription') as transcription_length
FROM notes n
JOIN blocks b ON n.id = b.note_id
WHERE b.type = 'AUDIO'
ORDER BY n.created_at;

-- Test 10: Verify user isolation (should return empty for non-existent user)
SELECT COUNT(*) as should_be_zero
FROM notes 
WHERE user_id NOT IN (SELECT id FROM profiles);
