{"expo": {"name": "note-app", "slug": "note-app", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "scheme": "note-app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.rp.noteapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/images/icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "com.rp.noteapp"}, "web": {"bundler": "metro", "output": "server", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/images/icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", ["expo-build-properties", {"ios": {"deploymentTarget": "16.0"}}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], "expo-asset", "expo-web-browser", ["expo-audio", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "233e65f4-f8cd-4eea-b4ad-a739f34b9984"}}}}