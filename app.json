{"expo": {"name": "AI-lessons app", "slug": "expo-convex-clerk-template", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/icon.png", "scheme": "expostarter", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.rp.expo-starter"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"], "package": "com.rp.expostarter"}, "web": {"bundler": "metro", "output": "server", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", ["expo-build-properties", {"ios": {"deploymentTarget": "16.0"}}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], "expo-asset", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "64d92f6b-d3a8-47b4-a894-38cb700741a6"}}}}